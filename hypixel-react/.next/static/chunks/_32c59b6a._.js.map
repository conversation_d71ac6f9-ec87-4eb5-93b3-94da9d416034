{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Hooks.tsx"], "sourcesContent": ["import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from 'react'\nimport api from '../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../api/ApiTypes.d'\nimport { getCurrentCoflCoins, subscribeToCoflcoinChange } from './CoflCoinsUtils'\nimport { isClientSideRendering } from './SSRUtils'\nimport { getURLSearchParam } from './Parser/URLParser'\nimport { useRouter } from 'next/navigation'\n\nexport function useForceUpdate() {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const [update, setUpdate] = useState(0)\n    return () => setUpdate(update => update + 1)\n}\n\nexport function useSwipe(onSwipeUp?: Function, onSwipeRight?: Function, onSwipeDown?: Function, onSwipeLeft?: Function) {\n    if (!isClientSideRendering()) {\n        return\n    }\n\n    document.addEventListener('touchstart', handleTouchStart, false)\n    document.addEventListener('touchmove', handleTouchMove, false)\n\n    var xDown = null\n    var yDown = null\n\n    function getTouches(evt) {\n        return (\n            evt.touches || // browser API\n            evt.originalEvent.touches\n        ) // jQuery\n    }\n\n    function handleTouchStart(evt) {\n        const firstTouch = getTouches(evt)[0]\n        xDown = firstTouch.clientX\n        yDown = firstTouch.clientY\n    }\n\n    function handleTouchMove(evt) {\n        if (xDown === null || yDown === null) {\n            return\n        }\n\n        var xUp = evt.touches[0].clientX\n        var yUp = evt.touches[0].clientY\n\n        var xDiff = xDown! - xUp\n        var yDiff = yDown! - yUp\n\n        if (Math.abs(xDiff) > Math.abs(yDiff)) {\n            /*most significant*/\n            if (xDiff > 0) {\n                if (onSwipeLeft) {\n                    onSwipeLeft()\n                }\n            } else {\n                if (onSwipeRight) {\n                    onSwipeRight()\n                }\n            }\n        } else {\n            if (yDiff > 0) {\n                if (onSwipeUp) {\n                    onSwipeUp()\n                }\n            } else {\n                if (onSwipeDown) {\n                    onSwipeDown()\n                }\n            }\n        }\n        /* reset values */\n        xDown = null\n        yDown = null\n    }\n\n    return () => {\n        document.removeEventListener('touchstart', handleTouchStart, false)\n        document.removeEventListener('touchmove', handleTouchMove, false)\n    }\n}\n\nexport function useCoflCoins() {\n    const [coflCoins, setCoflCoins] = useState(getCurrentCoflCoins())\n\n    useEffect(() => {\n        let unsubscribe = subscribeToCoflcoinChange(setCoflCoins)\n\n        return () => {\n            unsubscribe()\n        }\n    }, [])\n\n    return coflCoins\n}\n\nexport function useWasAlreadyLoggedIn() {\n    const [wasAlreadyLoggedIn, setWasAlreadyLoggedIn] = useState(false)\n    useEffect(() => {\n        setWasAlreadyLoggedIn(localStorage.getItem('googleId') !== null)\n    }, [])\n\n    return wasAlreadyLoggedIn\n}\n\nexport function useDebounce(value, delay) {\n    const [debouncedValue, setDebouncedValue] = useState(value)\n    useEffect(() => {\n        const handler = setTimeout(() => {\n            setDebouncedValue(value)\n        }, delay)\n        return () => {\n            clearTimeout(handler)\n        }\n    }, [value, delay])\n    return debouncedValue\n}\n\ntype ReadonlyRef<T> = {\n    readonly current: T\n}\n\nexport function useStateWithRef<T>(defaultValue: T): [T, Dispatch<SetStateAction<T>>, ReadonlyRef<T>] {\n    const [state, _setState] = useState(defaultValue)\n    let stateRef = useRef(state)\n\n    const setState: typeof _setState = useCallback((newState: T) => {\n        stateRef.current = newState\n        _setState(newState)\n    }, [])\n\n    return [state, setState, stateRef]\n}\n\nexport function useQueryParamState<T>(key: string, defaultValue: T): [T, Dispatch<SetStateAction<T>>] {\n    const [state, setState] = useState<T>(getDefaultValue() || defaultValue)\n    const router = useRouter()\n\n    function getDefaultValue(): T | undefined {\n        let param = getURLSearchParam(key)\n        if (!param) {\n            return undefined\n        }\n        return JSON.parse(decodeURIComponent(param)) as T\n    }\n\n    function _setState(newState: T) {\n        setState(newState)\n        let urlparams = new URLSearchParams(window.location.search)\n        if (!newState) {\n            urlparams.delete(key)\n        } else {\n            urlparams.set(key, encodeURIComponent(JSON.stringify(newState)))\n        }\n        router.replace(`${window.location.pathname}?${urlparams.toString()}`)\n    }\n\n    return [state, _setState]\n}\n\nexport function useIsMobile() {\n    let [isMobile, setIsMobile] = useState(false)\n\n    useEffect(() => {\n        setIsMobile(isMobileCheck())\n    }, [])\n\n    function isMobileCheck() {\n        let check = false\n        // eslint-disable-next-line no-useless-escape\n        ;(function (a) {\n            if (\n                /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(\n                    a\n                ) ||\n                /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i.test(\n                    a.substr(0, 4)\n                )\n            )\n                check = true\n        })(navigator.userAgent || navigator.vendor || (window as any).opera)\n        return check\n    }\n\n    return isMobile\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAGA;AACA;AACA;AACA;;;;;;;AAEO,SAAS;;IACZ,6DAA6D;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO,IAAM,UAAU,CAAA,SAAU,SAAS;AAC9C;GAJgB;AAMT,SAAS,SAAS,SAAoB,EAAE,YAAuB,EAAE,WAAsB,EAAE,WAAsB;IAClH,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B;IACJ;IAEA,SAAS,gBAAgB,CAAC,cAAc,kBAAkB;IAC1D,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;IAExD,IAAI,QAAQ;IACZ,IAAI,QAAQ;IAEZ,SAAS,WAAW,GAAG;QACnB,OACI,IAAI,OAAO,IAAI,cAAc;QAC7B,IAAI,aAAa,CAAC,OAAO,EAC3B,SAAS;IACf;IAEA,SAAS,iBAAiB,GAAG;QACzB,MAAM,aAAa,WAAW,IAAI,CAAC,EAAE;QACrC,QAAQ,WAAW,OAAO;QAC1B,QAAQ,WAAW,OAAO;IAC9B;IAEA,SAAS,gBAAgB,GAAG;QACxB,IAAI,UAAU,QAAQ,UAAU,MAAM;YAClC;QACJ;QAEA,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO;QAChC,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO;QAEhC,IAAI,QAAQ,QAAS;QACrB,IAAI,QAAQ,QAAS;QAErB,IAAI,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,QAAQ;YACnC,kBAAkB,GAClB,IAAI,QAAQ,GAAG;gBACX,IAAI,aAAa;oBACb;gBACJ;YACJ,OAAO;gBACH,IAAI,cAAc;oBACd;gBACJ;YACJ;QACJ,OAAO;YACH,IAAI,QAAQ,GAAG;gBACX,IAAI,WAAW;oBACX;gBACJ;YACJ,OAAO;gBACH,IAAI,aAAa;oBACb;gBACJ;YACJ;QACJ;QACA,gBAAgB,GAChB,QAAQ;QACR,QAAQ;IACZ;IAEA,OAAO;QACH,SAAS,mBAAmB,CAAC,cAAc,kBAAkB;QAC7D,SAAS,mBAAmB,CAAC,aAAa,iBAAiB;IAC/D;AACJ;AAEO,SAAS;;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,cAAc,CAAA,GAAA,2HAAA,CAAA,4BAAyB,AAAD,EAAE;YAE5C;0CAAO;oBACH;gBACJ;;QACJ;iCAAG,EAAE;IAEL,OAAO;AACX;IAZgB;AAcT,SAAS;;IACZ,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACN,sBAAsB,aAAa,OAAO,CAAC,gBAAgB;QAC/D;0CAAG,EAAE;IAEL,OAAO;AACX;IAPgB;AAST,SAAS,YAAY,KAAK,EAAE,KAAK;;IACpC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,MAAM,UAAU;iDAAW;oBACvB,kBAAkB;gBACtB;gDAAG;YACH;yCAAO;oBACH,aAAa;gBACjB;;QACJ;gCAAG;QAAC;QAAO;KAAM;IACjB,OAAO;AACX;IAXgB;AAiBT,SAAS,gBAAmB,YAAe;;IAC9C,MAAM,CAAC,OAAO,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACpC,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEtB,MAAM,WAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC5C,SAAS,OAAO,GAAG;YACnB,UAAU;QACd;gDAAG,EAAE;IAEL,OAAO;QAAC;QAAO;QAAU;KAAS;AACtC;IAVgB;AAYT,SAAS,mBAAsB,GAAW,EAAE,YAAe;;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK,qBAAqB;IAC3D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,SAAS;QACL,IAAI,QAAQ,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC9B,IAAI,CAAC,OAAO;YACR,OAAO;QACX;QACA,OAAO,KAAK,KAAK,CAAC,mBAAmB;IACzC;IAEA,SAAS,UAAU,QAAW;QAC1B,SAAS;QACT,IAAI,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC1D,IAAI,CAAC,UAAU;YACX,UAAU,MAAM,CAAC;QACrB,OAAO;YACH,UAAU,GAAG,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC;QACzD;QACA,OAAO,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,QAAQ,IAAI;IACxE;IAEA,OAAO;QAAC;QAAO;KAAU;AAC7B;IAxBgB;;QAEG,qIAAA,CAAA,YAAS;;;AAwBrB,SAAS;;IACZ,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,YAAY;QAChB;gCAAG,EAAE;IAEL,SAAS;QACL,IAAI,QAAQ;QAEX,CAAC,SAAU,CAAC;YACT,IACI,2TAA2T,IAAI,CAC3T,MAEJ,0kDAA0kD,IAAI,CAC1kD,EAAE,MAAM,CAAC,GAAG,KAGhB,QAAQ;QAChB,CAAC,EAAE,UAAU,SAAS,IAAI,UAAU,MAAM,IAAI,AAAC,OAAe,KAAK;QACnE,OAAO;IACX;IAEA,OAAO;AACX;IAzBgB", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"googleButton\": \"GoogleSignIn-module__lTSYOa__googleButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/GoogleSignIn/GoogleSignIn.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { toast } from 'react-toastify'\nimport api from '../../api/ApiHelper'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { CUSTOM_EVENTS } from '../../api/ApiTypes.d'\nimport { GoogleLogin } from '@react-oauth/google'\nimport styles from './GoogleSignIn.module.css'\nimport { GOOGLE_EMAIL, GOOGLE_NAME, GOOGLE_PROFILE_PICTURE_URL, setSetting } from '../../utils/SettingsUtils'\nimport { atobUnicode } from '../../utils/Base64Utils'\nimport { Modal } from 'react-bootstrap'\n\ninterface Props {\n    onAfterLogin?(): void\n    onLoginFail?(): void\n    onManualLoginClick?(): void\n    rerenderFlip?: number\n}\n\nfunction GoogleSignIn(props: Props) {\n    let [wasAlreadyLoggedInThisSession, setWasAlreadyLoggedInThisSession] = useState(\n        isClientSideRendering() ? isValidTokenAvailable(localStorage.getItem('googleId')) : false\n    )\n\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [isSSR, setIsSSR] = useState(true)\n    let [isLoginNotShowing, setIsLoginNotShowing] = useState(false)\n    let [showButtonNotRenderingModal, setShowButtonNotRenderingModal] = useState(false)\n    let { trackEvent } = useMatomo()\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSSR(false)\n        if (wasAlreadyLoggedInThisSession) {\n            let token = localStorage.getItem('googleId')!\n            let userObject = JSON.parse(atobUnicode(token.split('.')[1]))\n            setSetting(GOOGLE_EMAIL, userObject.email)\n            onLoginSucces(token)\n        } else {\n            setTimeout(() => {\n                let isShown = false\n                document.querySelectorAll('iframe').forEach(e => {\n                    if (e.src && e.src.includes('accounts.google.com')) {\n                        isShown = true\n                    }\n                })\n                if (!isShown) {\n                    setIsLoggedIn(false)\n                    setIsLoginNotShowing(true)\n                    sessionStorage.removeItem('googleId')\n                    localStorage.removeItem('googleId')\n                }\n            }, 5000)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    useEffect(() => {\n        if (wasAlreadyLoggedInThisSession) {\n            setIsLoggedIn(true)\n        }\n    }, [wasAlreadyLoggedInThisSession])\n\n    useEffect(() => {\n        forceUpdate()\n        setIsLoggedIn(sessionStorage.getItem('googleId') !== null)\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.rerenderFlip])\n\n    function onLoginSucces(token: string) {\n        setIsLoggedIn(true)\n        api.loginWithToken(token)\n            .then(token => {\n                localStorage.setItem('googleId', token)\n                sessionStorage.setItem('googleId', token)\n                let refId = (window as any).refId\n                if (refId) {\n                    api.setRef(refId)\n                }\n                document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.GOOGLE_LOGIN))\n                if (props.onAfterLogin) {\n                    props.onAfterLogin()\n                }\n            })\n            .catch(error => {\n                // dont show the error message for the invalid token error\n                // the google sign component sometimes sends an outdated token, causing this error\n                if (error.slug !== 'invalid_token') {\n                    toast.error(`An error occoured while trying to sign in with Google. ${error ? error.slug || JSON.stringify(error) : null}`)\n                } else {\n                    console.warn('setGoogle: Invalid token error', error)\n                    sessionStorage.removeItem('googleId')\n                    localStorage.removeItem('googleId')\n                }\n                setIsLoggedIn(false)\n                setWasAlreadyLoggedInThisSession(false)\n                sessionStorage.removeItem('googleId')\n                localStorage.removeItem('googleId')\n            })\n    }\n\n    function onLoginFail() {\n        toast.error('Something went wrong, please try again.', { autoClose: 20000 })\n    }\n\n    function onLoginClick() {\n        if (props.onManualLoginClick) {\n            props.onManualLoginClick()\n        }\n        trackEvent({\n            category: 'login',\n            action: 'click'\n        })\n    }\n\n    let style: React.CSSProperties = isLoggedIn\n        ? {\n              visibility: 'collapse',\n              height: 0\n          }\n        : {}\n\n    if (isSSR) {\n        return null\n    }\n\n    let buttonNotRenderingModal = (\n        <Modal\n            show={showButtonNotRenderingModal}\n            onHide={() => {\n                setShowButtonNotRenderingModal(false)\n            }}\n        >\n            <Modal.Header>\n                <Modal.Title>Google Login button not showing up?</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <p>This is most likely caused by either an external software like an anti virus or your browser/extension blocking it.</p>\n                <hr />\n                <p>Known issues:</p>\n                <ul>\n                    <li>Kaspersky's \"Secure Browse\" feature seems to block the Google login.</li>\n                    <li>Opera GX seems to sometimes blocks the login button. The specific setting or reason on when it blocks it is unknown.</li>\n                </ul>\n            </Modal.Body>\n        </Modal>\n    )\n\n    return (\n        <div style={style} onClickCapture={onLoginClick}>\n            {!wasAlreadyLoggedInThisSession ? (\n                <>\n                    <div className={styles.googleButton}>\n                        {!isSSR ? (\n                            <GoogleLogin\n                                onSuccess={response => {\n                                    try {\n                                        let userObject = JSON.parse(atobUnicode(response.credential!.split('.')[1]))\n                                        setSetting(GOOGLE_PROFILE_PICTURE_URL, userObject.picture)\n                                        setSetting(GOOGLE_EMAIL, userObject.email)\n                                        setSetting(GOOGLE_NAME, userObject.name)\n                                    } catch {\n                                        toast.warn('Parsing issue with the google token. There might be issues when displaying details on the account page!')\n                                    }\n                                    onLoginSucces(response.credential!)\n                                }}\n                                onError={onLoginFail}\n                                theme={'filled_blue'}\n                                size={'large'}\n                                useOneTap\n                                auto_select\n                            />\n                        ) : null}\n                    </div>\n                    <p>\n                        I have read and agree to the <a href=\"https://coflnet.com/privacy\">Privacy Policy</a>\n                    </p>\n                    {isLoginNotShowing ? (\n                        <p>\n                            Login button not showing? Click{' '}\n                            <span\n                                style={{ color: '#007bff', cursor: 'pointer' }}\n                                onClick={() => {\n                                    setShowButtonNotRenderingModal(true)\n                                }}\n                            >\n                                here\n                            </span>\n                            .\n                        </p>\n                    ) : null}\n                </>\n            ) : null}\n            {buttonNotRenderingModal}\n        </div>\n    )\n}\n\nexport default GoogleSignIn\n\nexport function isValidTokenAvailable(token?: string | null) {\n    if (!token || token === 'null') {\n        return\n    }\n    try {\n        let details = JSON.parse(atobUnicode(token.split('.')[1]))\n        let expirationDate = new Date(parseInt(details.exp) * 1000)\n        return expirationDate.getTime() - 10000 > new Date().getTime()\n    } catch (e) {\n        toast.warn(\"Parsing issue with the google token. Can't automatically login!\")\n        return false\n    }\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;;AAqBA,SAAS,aAAa,KAAY;;IAC9B,IAAI,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3E,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,sBAAsB,aAAa,OAAO,CAAC,eAAe;IAGxF,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC7B,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,SAAS;YACT,IAAI,+BAA+B;gBAC/B,IAAI,QAAQ,aAAa,OAAO,CAAC;gBACjC,IAAI,aAAa,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3D,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY,EAAE,WAAW,KAAK;gBACzC,cAAc;YAClB,OAAO;gBACH;8CAAW;wBACP,IAAI,UAAU;wBACd,SAAS,gBAAgB,CAAC,UAAU,OAAO;sDAAC,CAAA;gCACxC,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,wBAAwB;oCAChD,UAAU;gCACd;4BACJ;;wBACA,IAAI,CAAC,SAAS;4BACV,cAAc;4BACd,qBAAqB;4BACrB,eAAe,UAAU,CAAC;4BAC1B,aAAa,UAAU,CAAC;wBAC5B;oBACJ;6CAAG;YACP;QACA,uDAAuD;QAC3D;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,+BAA+B;gBAC/B,cAAc;YAClB;QACJ;iCAAG;QAAC;KAA8B;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN;YACA,cAAc,eAAe,OAAO,CAAC,gBAAgB;QACrD,uDAAuD;QAC3D;iCAAG;QAAC,MAAM,YAAY;KAAC;IAEvB,SAAS,cAAc,KAAa;QAChC,cAAc;QACd,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,OACd,IAAI,CAAC,CAAA;YACF,aAAa,OAAO,CAAC,YAAY;YACjC,eAAe,OAAO,CAAC,YAAY;YACnC,IAAI,QAAQ,AAAC,OAAe,KAAK;YACjC,IAAI,OAAO;gBACP,oHAAA,CAAA,UAAG,CAAC,MAAM,CAAC;YACf;YACA,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,YAAY;YACjE,IAAI,MAAM,YAAY,EAAE;gBACpB,MAAM,YAAY;YACtB;QACJ,GACC,KAAK,CAAC,CAAA;YACH,0DAA0D;YAC1D,kFAAkF;YAClF,IAAI,MAAM,IAAI,KAAK,iBAAiB;gBAChC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uDAAuD,EAAE,QAAQ,MAAM,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS,MAAM;YAC9H,OAAO;gBACH,QAAQ,IAAI,CAAC,kCAAkC;gBAC/C,eAAe,UAAU,CAAC;gBAC1B,aAAa,UAAU,CAAC;YAC5B;YACA,cAAc;YACd,iCAAiC;YACjC,eAAe,UAAU,CAAC;YAC1B,aAAa,UAAU,CAAC;QAC5B;IACR;IAEA,SAAS;QACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2CAA2C;YAAE,WAAW;QAAM;IAC9E;IAEA,SAAS;QACL,IAAI,MAAM,kBAAkB,EAAE;YAC1B,MAAM,kBAAkB;QAC5B;QACA,WAAW;YACP,UAAU;YACV,QAAQ;QACZ;IACJ;IAEA,IAAI,QAA6B,aAC3B;QACI,YAAY;QACZ,QAAQ;IACZ,IACA,CAAC;IAEP,IAAI,OAAO;QACP,OAAO;IACX;IAEA,IAAI,wCACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,QAAQ;YACJ,+BAA+B;QACnC;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;0BACT,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;kCACP,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;;;;kCACD,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;0CACG,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;IAMpB,qBACI,6LAAC;QAAI,OAAO;QAAO,gBAAgB;;YAC9B,CAAC,8CACE;;kCACI,6LAAC;wBAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,YAAY;kCAC9B,CAAC,sBACE,6LAAC,qKAAA,CAAA,cAAW;4BACR,WAAW,CAAA;gCACP,IAAI;oCACA,IAAI,aAAa,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,UAAU,CAAE,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC1E,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,6BAA0B,EAAE,WAAW,OAAO;oCACzD,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY,EAAE,WAAW,KAAK;oCACzC,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,cAAW,EAAE,WAAW,IAAI;gCAC3C,EAAE,OAAM;oCACJ,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gCACf;gCACA,cAAc,SAAS,UAAU;4BACrC;4BACA,SAAS;4BACT,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,WAAW;;;;;mCAEf;;;;;;kCAER,6LAAC;;4BAAE;0CAC8B,6LAAC;gCAAE,MAAK;0CAA8B;;;;;;;;;;;;oBAEtE,kCACG,6LAAC;;4BAAE;4BACiC;0CAChC,6LAAC;gCACG,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAU;gCAC7C,SAAS;oCACL,+BAA+B;gCACnC;0CACH;;;;;;4BAEM;;;;;;+BAGX;;+BAER;YACH;;;;;;;AAGb;GAjLS;;QASgB,sNAAA,CAAA,YAAS;QACZ,kHAAA,CAAA,iBAAc;;;KAV3B;uCAmLM;AAER,SAAS,sBAAsB,KAAqB;IACvD,IAAI,CAAC,SAAS,UAAU,QAAQ;QAC5B;IACJ;IACA,IAAI;QACA,IAAI,UAAU,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACxD,IAAI,iBAAiB,IAAI,KAAK,SAAS,QAAQ,GAAG,IAAI;QACtD,OAAO,eAAe,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO;IAChE,EAAE,OAAO,GAAG;QACR,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACX,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/LoadingUtils.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport React, { type JSX } from 'react';\nimport { Spinner } from 'react-bootstrap'\n\nexport function getLoadingElement(text?: JSX.Element): JSX.Element {\n    return (\n        <div style={{ textAlign: 'center' }}>\n            <span>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n            </span>\n            {text ? text : <p>Loading Data...</p>}\n        </div>\n    )\n}\n\nexport function getInitialLoadingElement(): JSX.Element {\n    return (\n        <div className=\"main-loading\" style={{ height: '500px' }}>\n            <div>\n                <Image src=\"/logo192.png\" height=\"192\" width=\"192\" alt=\"auction house logo\" />\n                <div className=\"main-loading\">\n                    <span>Loading App...</span>\n                </div>\n            </div>\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEO,SAAS,kBAAkB,IAAkB;IAChD,qBACI,6LAAC;QAAI,OAAO;YAAE,WAAW;QAAS;;0BAC9B,6LAAC;;kCACG,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;kCAClC,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;kCAClC,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;;;;;;;YAErC,OAAO,qBAAO,6LAAC;0BAAE;;;;;;;;;;;;AAG9B;AAEO,SAAS;IACZ,qBACI,6LAAC;QAAI,WAAU;QAAe,OAAO;YAAE,QAAQ;QAAQ;kBACnD,cAAA,6LAAC;;8BACG,6LAAC,gIAAA,CAAA,UAAK;oBAAC,KAAI;oBAAe,QAAO;oBAAM,OAAM;oBAAM,KAAI;;;;;;8BACvD,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;kCAAK;;;;;;;;;;;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/NavBar/NavBar.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"hamburgerIcon\": \"NavBar-module__yBvhsG__hamburgerIcon\",\n  \"logo\": \"NavBar-module__yBvhsG__logo\",\n  \"menuItem\": \"NavBar-module__yBvhsG__menuItem\",\n  \"navBar\": \"NavBar-module__yBvhsG__navBar\",\n  \"navClosing\": \"NavBar-module__yBvhsG__navClosing\",\n  \"navOpen\": \"NavBar-module__yBvhsG__navOpen\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/NavBar/NavBar.tsx"], "sourcesContent": ["'use client'\nimport AccountBalanceIcon from '@mui/icons-material/AccountBalance'\nimport AccountIcon from '@mui/icons-material/AccountCircle'\nimport BuildIcon from '@mui/icons-material/Build'\nimport ChatIcon from '@mui/icons-material/Chat'\nimport DownloadIcon from '@mui/icons-material/Download'\nimport HomeIcon from '@mui/icons-material/Home'\nimport MenuIcon from '@mui/icons-material/Menu'\nimport NotificationIcon from '@mui/icons-material/NotificationsOutlined'\nimport PetsIcon from '@mui/icons-material/PetsOutlined'\nimport PolicyIcon from '@mui/icons-material/Policy'\nimport ShareIcon from '@mui/icons-material/ShareOutlined'\nimport StorefrontIcon from '@mui/icons-material/Storefront'\nimport CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport React, { useEffect, useState } from 'react'\nimport { Menu, MenuItem, Sidebar } from 'react-pro-sidebar'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport styles from './NavBar.module.css'\n\nlet resizePromise: NodeJS.Timeout | null = null\n\ninterface Props {\n    hamburgerIconStyle?: React.CSSProperties\n}\n\nfunction NavBar(props: Props) {\n    let [isWideOpen, setIsWideOpen] = useState(false)\n    let [isHovering, setIsHovering] = useState(false)\n    let [isSmall, setIsSmall] = useState(true)\n    let [collapsed, setCollapsed] = useState(true)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSmall(document.body.clientWidth < 1500)\n\n        window.addEventListener('resize', resizeHandler)\n\n        return () => {\n            window.removeEventListener('resize', resizeHandler)\n        }\n    }, [])\n\n    useEffect(() => {\n        if (isWideOpen) {\n            document.addEventListener('click', outsideClickHandler, true)\n        } else {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n\n        return () => {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [isWideOpen])\n\n    useEffect(() => {\n        setCollapsed(isCollapsed())\n    }, [isSmall, isWideOpen, isHovering])\n\n    function isCollapsed() {\n        if (isSmall) {\n            return false\n        }\n        return !isWideOpen && !isHovering\n    }\n\n    function outsideClickHandler(evt) {\n        const flyoutEl = document.getElementById('navBar')\n        const hamburgerEl = document.getElementById('hamburgerIcon')\n        let targetEl = evt.target\n\n        do {\n            if (targetEl === flyoutEl || targetEl === hamburgerEl) {\n                return\n            }\n            targetEl = (targetEl as any).parentNode\n        } while (targetEl)\n\n        if (isWideOpen) {\n            if (isSmall) {\n                let el = document.getElementById('pro-sidebar')\n                el?.classList.add(styles.navClosing)\n                el?.classList.remove(styles.navOpen)\n                setTimeout(() => {\n                    setIsWideOpen(false)\n                    el?.classList.remove(styles.navClosing)\n                }, 500)\n            } else {\n                setIsWideOpen(false)\n            }\n        }\n    }\n\n    function onMouseMove() {\n        setIsHovering(true)\n    }\n\n    function onMouseOut() {\n        setIsHovering(false)\n    }\n\n    function resizeHandler() {\n        if (resizePromise) {\n            return\n        }\n        resizePromise = setTimeout(() => {\n            setIsWideOpen(false)\n            setIsSmall(document.body.clientWidth < 1500)\n            forceUpdate()\n            resizePromise = null\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.style.left = '0px'\n            }\n        }, 500)\n    }\n\n    function onHamburgerClick() {\n        if (isSmall && !isWideOpen) {\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.hidden = false\n                el.style.left = '-270px'\n                setTimeout(() => {\n                    if (el) {\n                        el.classList.add(styles.navOpen)\n                    }\n                })\n                setTimeout(() => {\n                    setIsWideOpen(true)\n                }, 500)\n            }\n        } else {\n            setIsWideOpen(!isWideOpen)\n        }\n    }\n\n    return (\n        <span>\n            <aside className={styles.navBar} id=\"navBar\" onMouseEnter={onMouseMove} onMouseLeave={onMouseOut}>\n                <Sidebar id=\"pro-sidebar\" hidden={isSmall && !isWideOpen} backgroundColor=\"#1d1d1d\" collapsed={collapsed}>\n                    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n                        <div>\n                            <div className={styles.logo}>\n                                <Image src=\"/logo512.png\" alt=\"Logo\" width={40} height={40} style={{ translate: '-5px' }} /> {!isCollapsed() ? 'Coflnet' : ''}\n                            </div>\n                        </div>\n                        <hr />\n                        <Menu>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/'} />} icon={<HomeIcon />}>\n                                Home\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/flipper'} />} icon={<StorefrontIcon />}>\n                                Item Flipper\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/account'} />} icon={<AccountIcon />}>\n                                Account\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/subscriptions'} />} icon={<NotificationIcon />}>\n                                Notifier\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/crafts'} />} icon={<BuildIcon />}>\n                                Profitable Crafts\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/premium'} />} icon={<AccountBalanceIcon />}>\n                                Premium / Shop\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/trade'} />} icon={<CurrencyExchangeIcon />}>\n                                Trading\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/kat'} />} icon={<PetsIcon />}>\n                                Kat Flips\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/mod'} />} icon={<DownloadIcon />}>\n                                Mod\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/ref'} />} icon={<ShareIcon />}>\n                                Referral\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/about'} />} icon={<PolicyIcon />}>\n                                Links / Legal\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/feedback'} />} icon={<ChatIcon />}>\n                                Feedback\n                            </MenuItem>\n                            <MenuItem\n                                className={styles.menuItem}\n                                component={<Link href={'https://discord.gg/wvKXfTgCfb'} target=\"_blank\" />}\n                                rel=\"noreferrer\"\n                                icon={<Image src=\"/discord_icon.svg\" alt=\"Discord icon\" height={24} width={32} />}\n                            >\n                                Discord\n                            </MenuItem>\n                        </Menu>\n                    </div>\n                </Sidebar>\n            </aside>\n            {isSmall ? (\n                <span onClick={onHamburgerClick} className={styles.hamburgerIcon} id=\"hamburgerIcon\" style={props.hamburgerIconStyle}>\n                    <MenuIcon fontSize=\"large\" />\n                </span>\n            ) : (\n                ''\n            )}\n        </span>\n    )\n}\n\nexport default NavBar\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,gBAAuC;AAM3C,SAAS,OAAO,KAAY;;IACxB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YAEvC,OAAO,gBAAgB,CAAC,UAAU;YAElC;oCAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;gBACzC;;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,YAAY;gBACZ,SAAS,gBAAgB,CAAC,SAAS,qBAAqB;YAC5D,OAAO;gBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;YAC/D;YAEA;oCAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;gBAC/D;;QACA,uDAAuD;QAC3D;2BAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,aAAa;QACjB;2BAAG;QAAC;QAAS;QAAY;KAAW;IAEpC,SAAS;QACL,IAAI,SAAS;YACT,OAAO;QACX;QACA,OAAO,CAAC,cAAc,CAAC;IAC3B;IAEA,SAAS,oBAAoB,GAAG;QAC5B,MAAM,WAAW,SAAS,cAAc,CAAC;QACzC,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,IAAI,WAAW,IAAI,MAAM;QAEzB,GAAG;YACC,IAAI,aAAa,YAAY,aAAa,aAAa;gBACnD;YACJ;YACA,WAAW,AAAC,SAAiB,UAAU;QAC3C,QAAS,SAAS;QAElB,IAAI,YAAY;YACZ,IAAI,SAAS;gBACT,IAAI,KAAK,SAAS,cAAc,CAAC;gBACjC,IAAI,UAAU,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU;gBACnC,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,OAAO;gBACnC,WAAW;oBACP,cAAc;oBACd,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;gBAC1C,GAAG;YACP,OAAO;gBACH,cAAc;YAClB;QACJ;IACJ;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,IAAI,eAAe;YACf;QACJ;QACA,gBAAgB,WAAW;YACvB,cAAc;YACd,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YACvC;YACA,gBAAgB;YAChB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,KAAK,CAAC,IAAI,GAAG;YACpB;QACJ,GAAG;IACP;IAEA,SAAS;QACL,IAAI,WAAW,CAAC,YAAY;YACxB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,MAAM,GAAG;gBACZ,GAAG,KAAK,CAAC,IAAI,GAAG;gBAChB,WAAW;oBACP,IAAI,IAAI;wBACJ,GAAG,SAAS,CAAC,GAAG,CAAC,6IAAA,CAAA,UAAM,CAAC,OAAO;oBACnC;gBACJ;gBACA,WAAW;oBACP,cAAc;gBAClB,GAAG;YACP;QACJ,OAAO;YACH,cAAc,CAAC;QACnB;IACJ;IAEA,qBACI,6LAAC;;0BACG,6LAAC;gBAAM,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;gBAAE,IAAG;gBAAS,cAAc;gBAAa,cAAc;0BAClF,cAAA,6LAAC,iKAAA,CAAA,UAAO;oBAAC,IAAG;oBAAc,QAAQ,WAAW,CAAC;oBAAY,iBAAgB;oBAAU,WAAW;8BAC3F,cAAA,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAQ,SAAS;4BAAQ,eAAe;wBAAS;;0CACnE,6LAAC;0CACG,cAAA,6LAAC;oCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,IAAI;;sDACvB,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAe,KAAI;4CAAO,OAAO;4CAAI,QAAQ;4CAAI,OAAO;gDAAE,WAAW;4CAAO;;;;;;wCAAK;wCAAE,CAAC,gBAAgB,YAAY;;;;;;;;;;;;0CAGnI,6LAAC;;;;;0CACD,6LAAC,iKAAA,CAAA,OAAI;;kDACD,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAS,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG1F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;kDAAK;;;;;;kDAGvG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,qKAAA,CAAA,UAAW;;;;;kDAAK;;;;;;kDAGpG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAsB,oBAAM,6LAAC,6KAAA,CAAA,UAAgB;;;;;kDAAK;;;;;;kDAG/G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAe,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,sKAAA,CAAA,UAAkB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,wKAAA,CAAA,UAAoB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,oKAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG7F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,gKAAA,CAAA,UAAY;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,qKAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAG9F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,8JAAA,CAAA,UAAU;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAiB,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAGlG,6LAAC,iKAAA,CAAA,WAAQ;wCACL,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAC1B,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;4CAAiC,QAAO;;;;;;wCAC/D,KAAI;wCACJ,oBAAM,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAoB,KAAI;4CAAe,QAAQ;4CAAI,OAAO;;;;;;kDAC9E;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOhB,wBACG,6LAAC;gBAAK,SAAS;gBAAkB,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;gBAAE,IAAG;gBAAgB,OAAO,MAAM,kBAAkB;0BAChH,cAAA,6LAAC,4JAAA,CAAA,UAAQ;oBAAC,UAAS;;;;;;;;;;uBAGvB;;;;;;;AAIhB;GArLS;;QAKa,kHAAA,CAAA,iBAAc;;;KAL3B;uCAuLM", "debugId": null}}, {"offset": {"line": 1230, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Number/Number.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { numberWithThousandsSeparators } from '../../utils/Formatter'\n\ninterface Props {\n    number: number | string\n}\n\nexport default function NumberElement(props: Props) {\n    let [isSSR, setIsSSR] = useState(true)\n\n    let value = Number(props.number)\n\n    useEffect(() => {\n        setIsSSR(false)\n    }, [])\n\n    return <>{isSSR ? numberWithThousandsSeparators(value, ',', '.') : numberWithThousandsSeparators(value)}</>\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAQe,SAAS,cAAc,KAAY;;IAC9C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,IAAI,QAAQ,OAAO,MAAM,MAAM;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,SAAS;QACb;kCAAG,EAAE;IAEL,qBAAO;kBAAG,QAAQ,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO,KAAK,OAAO,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE;;AACrG;GAVwB;KAAA", "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/PremiumFeatures/PremiumFeatures.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"featureCard\": \"PremiumFeatures-module__Tm37xq__featureCard\",\n  \"featureColumn\": \"PremiumFeatures-module__Tm37xq__featureColumn\",\n  \"featureColumnHeading\": \"PremiumFeatures-module__Tm37xq__featureColumnHeading\",\n  \"ingamePriceHoverImage\": \"PremiumFeatures-module__Tm37xq__ingamePriceHoverImage\",\n  \"premiumFeatures\": \"PremiumFeatures-module__Tm37xq__premiumFeatures\",\n  \"premiumProductColumn\": \"PremiumFeatures-module__Tm37xq__premiumProductColumn\",\n  \"premiumProductHeading\": \"PremiumFeatures-module__Tm37xq__premiumProductHeading\",\n  \"tooltipHoverId\": \"PremiumFeatures-module__Tm37xq__tooltipHoverId\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/PremiumFeatures/PremiumFeatures.tsx"], "sourcesContent": ["'use client'\nimport HelpIcon from '@mui/icons-material/Help'\nimport Link from 'next/link'\nimport { Table } from 'react-bootstrap'\nimport Number from '../../Number/Number'\nimport Tooltip from '../../Tooltip/Tooltip'\nimport styles from './PremiumFeatures.module.css'\nimport Image from 'next/image'\n\nfunction PremiumFeatures() {\n    let checkIconSvg = (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"lime\" className=\"bi bi-check-circle-fill\" viewBox=\"0 0 16 16\">\n            <path d=\"M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z\" />\n        </svg>\n    )\n    let checkIconElement = (\n        <td className={styles.premiumProductColumn}>\n            {checkIconSvg}\n        </td>\n    )\n\n    let xIconElement = (\n        <td className={styles.premiumProductColumn}>\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"red\" className=\"bi bi-x-circle-fill\" viewBox=\"0 0 16 16\">\n                <path d=\"M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z\" />\n            </svg>\n        </td>\n    )\n\n    return (\n        <div className={styles.premiumFeatures}>\n            <Table>\n                <thead>\n                    <tr>\n                        <th className={styles.featureColumnHeading}>Feature</th>\n                        <th className={styles.premiumProductHeading}>Free</th>\n                        <th className={styles.premiumProductHeading}>Starter</th>\n                        <th className={styles.premiumProductHeading}>Premium</th>\n                        <th className={styles.premiumProductHeading}>Premium+</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    <tr>\n                        <td className={styles.featureColumn}>Price History</td>\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Bazaar Data History</td>\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Auction Explorer</td>\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Player Auction History</td>\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Display Active Auctions</td>\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>\n                            Price estimations in game\n                            <Tooltip\n                                id={styles.tooltipHoverId}\n                                content={\n                                    <span style={{ marginLeft: '5px' }}>\n                                        <HelpIcon />\n                                    </span>\n                                }\n                                type=\"hover\"\n                                tooltipContent={\n                                    <div className={styles.ingamePriceHoverImage}>\n                                        <Image fill src=\"/price-estimation-ingame.png\" alt=\"Price Estimation Tooltip\" />\n                                    </div>\n                                }\n                            />\n                        </td>\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Top 3 Kat Flips</td>\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Top 3 Craft Flips</td>\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Full Access to Flipper Filters</td>\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Filter player auctions</td>\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>\n                            Look at `buyspeed` and `most profit` leaderboard\n                            <Tooltip\n                                content={\n                                    <span style={{ marginLeft: '5px' }}>\n                                        <HelpIcon />\n                                    </span>\n                                }\n                                type=\"hover\"\n                                tooltipContent={<p>This is currently available in our mod via the command /cl leaderbaord and /cl buyspeedboard</p>}\n                            />\n                        </td>\n                        {xIconElement}\n                        {xIconElement}\n                        {xIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>\n                            Replay flips{' '}\n                            <Tooltip\n                                content={\n                                    <span style={{ marginLeft: '5px' }}>\n                                        <HelpIcon />\n                                    </span>\n                                }\n                                type=\"hover\"\n                                tooltipContent={<p>Replay all active auctions against your flip filter to find flips that were created while you were offline</p>}\n                            />\n                        </td>\n                        {xIconElement}\n                        {xIconElement}\n                        {xIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Support the Project</td>\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>\n                            List of low supply items (<Link href=\"/lowSupply\">here</Link>)\n                        </td>\n                        {xIconElement}\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Priority Feature Request</td>\n                        {xIconElement}\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Discord Role</td>\n                        {xIconElement}\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Longer flip-tracking history</td>\n                        {xIconElement}\n                        {xIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Max. Recent/Active Auctions</td>\n                        <td className={styles.premiumProductColumn}>12</td>\n                        <td className={styles.premiumProductColumn}>60</td>\n                        <td className={styles.premiumProductColumn}>120</td>\n                        <td className={styles.premiumProductColumn}>120</td>\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Max. Notifications</td>\n                        <td className={styles.premiumProductColumn}>3</td>\n                        <td className={styles.premiumProductColumn}>10</td>\n                        <td className={styles.premiumProductColumn}>100</td>\n                        <td className={styles.premiumProductColumn}>\n                            <Number number={1000} />\n                        </td>\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Refresh time of /cofl cheapmuseum </td>\n                        <td className={styles.premiumProductColumn}>2 h</td>\n                        <td className={styles.premiumProductColumn}>5 min</td>\n                        <td className={styles.premiumProductColumn}>2 min</td>\n                        <td className={styles.premiumProductColumn}>2 min</td>\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Use /cofl forge in game</td>\n                        <td className={styles.premiumProductColumn}><Tooltip\n                            content={\n                                <span style={{ marginLeft: '5px' }}>\n                                    {checkIconSvg}<HelpIcon />\n                                </span>\n                            }\n                            type=\"hover\"\n                            tooltipContent={<p>The top 3 options require a paid plan</p>}\n                        /></td>\n                        {checkIconElement}\n                        {checkIconElement}\n                        {checkIconElement}\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>Chat Colors</td>\n                        <td className={styles.premiumProductColumn} style={{ color: 'gray', fontWeight: 'bold' }}>\n                            Gray\n                        </td>\n                        <td className={styles.premiumProductColumn} style={{ color: 'white', fontWeight: 'bold' }}>\n                            White\n                        </td>\n                        <td className={styles.premiumProductColumn} style={{ color: '#32de84', fontWeight: 'bold' }}>\n                            Green\n                        </td>\n                        <td className={styles.premiumProductColumn} style={{ color: '#ffaa00', fontWeight: 'bold' }}>\n                            Gold\n                        </td>\n                    </tr>\n                    <tr>\n                        <td className={styles.featureColumn}>\n                            Average flip receive time\n                            <Tooltip\n                                content={\n                                    <span style={{ marginLeft: '5px' }}>\n                                        <HelpIcon />\n                                    </span>\n                                }\n                                type=\"hover\"\n                                tooltipContent={\n                                    <p>\n                                        The Hypixel Auctions API updates once every 60 seconds. After we were able to load new auctions, this is how long it\n                                        will take until they are shown to you. (Parsing auctions, finding references, comparing to determine profit,\n                                        distributing and filtering and sending to you)\n                                    </p>\n                                }\n                            />\n                        </td>\n                        <td className={styles.premiumProductColumn}>40 sec</td>\n                        <td className={styles.premiumProductColumn}>10-20 sec</td>\n                        <td className={styles.premiumProductColumn}>~1 sec</td>\n                        <td className={styles.premiumProductColumn}>&lt; 1 sec</td>\n                    </tr>\n                </tbody>\n            </Table>\n        </div>\n    )\n}\n\nexport default PremiumFeatures\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AASA,SAAS;IACL,IAAI,6BACA,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,MAAK;QAAO,WAAU;QAA0B,SAAQ;kBACnH,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;IAGhB,IAAI,iCACA,6LAAC;QAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;kBACrC;;;;;;IAIT,IAAI,6BACA,6LAAC;QAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;kBACtC,cAAA,6LAAC;YAAI,OAAM;YAA6B,OAAM;YAAK,QAAO;YAAK,MAAK;YAAM,WAAU;YAAsB,SAAQ;sBAC9G,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;;;;;;IAKpB,qBACI,6LAAC;QAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,eAAe;kBAClC,cAAA,6LAAC,yLAAA,CAAA,QAAK;;8BACF,6LAAC;8BACG,cAAA,6LAAC;;0CACG,6LAAC;gCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;0CAAE;;;;;;0CAC5C,6LAAC;gCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,qBAAqB;0CAAE;;;;;;0CAC7C,6LAAC;gCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,qBAAqB;0CAAE;;;;;;0CAC7C,6LAAC;gCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,qBAAqB;0CAAE;;;;;;0CAC7C,6LAAC;gCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,qBAAqB;0CAAE;;;;;;;;;;;;;;;;;8BAGrD,6LAAC;;sCACG,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;;wCAAE;sDAEjC,6LAAC,oIAAA,CAAA,UAAO;4CACJ,IAAI,0KAAA,CAAA,UAAM,CAAC,cAAc;4CACzB,uBACI,6LAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAC7B,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;4CAGjB,MAAK;4CACL,8BACI,6LAAC;gDAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,qBAAqB;0DACxC,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDAAC,IAAI;oDAAC,KAAI;oDAA+B,KAAI;;;;;;;;;;;;;;;;;;;;;;gCAKlE;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;;wCAAE;sDAEjC,6LAAC,oIAAA,CAAA,UAAO;4CACJ,uBACI,6LAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAC7B,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;4CAGjB,MAAK;4CACL,8BAAgB,6LAAC;0DAAE;;;;;;;;;;;;;;;;;gCAG1B;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;;wCAAE;wCACpB;sDACb,6LAAC,oIAAA,CAAA,UAAO;4CACJ,uBACI,6LAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAC7B,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;4CAGjB,MAAK;4CACL,8BAAgB,6LAAC;0DAAE;;;;;;;;;;;;;;;;;gCAG1B;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;;wCAAE;sDACP,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAa;;;;;;wCAAW;;;;;;;gCAEhE;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;gCACpC;gCACA;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;8CACrC,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;;;;;;;sCAEhD,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;8CACrC,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CACtC,cAAA,6LAAC,kIAAA,CAAA,UAAM;wCAAC,QAAQ;;;;;;;;;;;;;;;;;sCAGxB,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;8CACrC,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;;;;;;;sCAEhD,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;8CACrC,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE,cAAA,6LAAC,oIAAA,CAAA,UAAO;wCAChD,uBACI,6LAAC;4CAAK,OAAO;gDAAE,YAAY;4CAAM;;gDAC5B;8DAAa,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;;wCAG/B,MAAK;wCACL,8BAAgB,6LAAC;sDAAE;;;;;;;;;;;;;;;;gCAEtB;gCACA;gCACA;;;;;;;sCAEL,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;8CAAE;;;;;;8CACrC,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;oCAAE,OAAO;wCAAE,OAAO;wCAAQ,YAAY;oCAAO;8CAAG;;;;;;8CAG1F,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;oCAAE,OAAO;wCAAE,OAAO;wCAAS,YAAY;oCAAO;8CAAG;;;;;;8CAG3F,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;oCAAE,OAAO;wCAAE,OAAO;wCAAW,YAAY;oCAAO;8CAAG;;;;;;8CAG7F,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;oCAAE,OAAO;wCAAE,OAAO;wCAAW,YAAY;oCAAO;8CAAG;;;;;;;;;;;;sCAIjG,6LAAC;;8CACG,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,aAAa;;wCAAE;sDAEjC,6LAAC,oIAAA,CAAA,UAAO;4CACJ,uBACI,6LAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAC7B,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;4CAGjB,MAAK;4CACL,8BACI,6LAAC;0DAAE;;;;;;;;;;;;;;;;;8CAQf,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;8CAC5C,6LAAC;oCAAG,WAAW,0KAAA,CAAA,UAAM,CAAC,oBAAoB;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpE;KAjRS;uCAmRM", "debugId": null}}, {"offset": {"line": 2247, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/Premium.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cancellationRightCheckbox\": \"Premium-module__qLYXbW__cancellationRightCheckbox\",\n  \"label\": \"Premium-module__qLYXbW__label\",\n  \"premiumPayment\": \"Premium-module__qLYXbW__premiumPayment\",\n  \"premiumPrice\": \"Premium-module__qLYXbW__premiumPrice\",\n  \"premiumProduct\": \"Premium-module__qLYXbW__premiumProduct\",\n  \"premiumProductLabel\": \"Premium-module__qLYXbW__premiumProductLabel\",\n  \"premiumProducts\": \"Premium-module__qLYXbW__premiumProducts\",\n  \"purchaseCard\": \"Premium-module__qLYXbW__purchaseCard\",\n  \"sendCoflCoinsButton\": \"Premium-module__qLYXbW__sendCoflCoinsButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2263, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/CoflCoins/CoflCoinsPurchase.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"discount\": \"CoflCoinsPurchase-module__25Dhdq__discount\",\n  \"manualRedirectLink\": \"CoflCoinsPurchase-module__25Dhdq__manualRedirectLink\",\n  \"paymentButton\": \"CoflCoinsPurchase-module__25Dhdq__paymentButton\",\n  \"paymentButtonWrapper\": \"CoflCoinsPurchase-module__25Dhdq__paymentButtonWrapper\",\n  \"paymentLabel\": \"CoflCoinsPurchase-module__25Dhdq__paymentLabel\",\n  \"paymentOption\": \"CoflCoinsPurchase-module__25Dhdq__paymentOption\",\n  \"premiumPlanCard\": \"CoflCoinsPurchase-module__25Dhdq__premiumPlanCard\",\n  \"premiumPrice\": \"CoflCoinsPurchase-module__25Dhdq__premiumPrice\",\n  \"productGrid\": \"CoflCoinsPurchase-module__25Dhdq__productGrid\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2280, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CoflCoins/GenericProviderPurchaseCard.tsx"], "sourcesContent": ["import { Button } from 'react-bootstrap'\nimport Tooltip from '../Tooltip/Tooltip'\nimport styles from './CoflCoinsPurchase.module.css'\nimport HelpIcon from '@mui/icons-material/Help'\nimport Number from '../Number/Number'\nimport type { JSX } from \"react\";\n\ninterface Props {\n    type: 'PayPal' | 'Stripe' | 'LemonSqueezy'\n    price: number\n    disabledTooltip: JSX.Element | undefined\n    onPay()\n    isDisabled: boolean\n    isRedirecting: boolean\n    redirectLink?: string\n    discount?: number\n}\n\nexport default function GenericProviderPurchaseCard(props: Props) {\n    function getRoundedPrice(price: number) {\n        return Math.round(price * 100) / 100\n    }\n    return (\n        <div className={styles.paymentOption}>\n            <div className={styles.paymentLabel}>\n                {props.type === 'PayPal' ? (\n                    <Tooltip\n                        content={\n                            <>\n                                Buy with {props.type}{' '}\n                                <span style={{ marginLeft: '5px' }}>\n                                    <HelpIcon />\n                                </span>\n                            </>\n                        }\n                        type=\"hover\"\n                        tooltipContent={<p>Higher price than with credit card due to higher fees</p>}\n                    />\n                ) : null}\n                {props.type === 'Stripe' && <span>Buy with other payment methods</span>}\n                {props.type === 'LemonSqueezy' && <span>Continue to payment</span>}\n            </div>\n            <Tooltip\n                type=\"hover\"\n                tooltipContent={props.disabledTooltip}\n                content={\n                    <div className={styles.paymentButtonWrapper}>\n                        <Button\n                            variant=\"success\"\n                            onClick={() => {\n                                props.onPay()\n                            }}\n                            className={styles.paymentButton}\n                            disabled={props.isDisabled}\n                        >\n                            {props.isRedirecting ? (\n                                <p className={styles.manualRedirectLink}>\n                                    {props.redirectLink ? (\n                                        <>\n                                            Redirecting to PayPal...\n                                            <br /> Not working?{' '}\n                                            <a\n                                                href={props.redirectLink}\n                                                onClick={e => {\n                                                    e.stopPropagation()\n                                                }}\n                                                target=\"_blank\"\n                                            >\n                                                Click here\n                                            </a>\n                                        </>\n                                    ) : (\n                                        <span>Contacting payment provider...</span>\n                                    )}\n                                </p>\n                            ) : (\n                                <span>\n                                    <Number number={getRoundedPrice(props.discount ? props.price * props.discount : props.price)} /> Euro\n                                    {props.discount ? (\n                                        <span style={{ color: 'red', fontWeight: 'bold', paddingLeft: '20px' }}>\n                                            {Math.round((1 - props.discount) * 100)}% OFF\n                                        </span>\n                                    ) : null}\n                                    {props.discount ? (\n                                        <p style={{ fontSize: 'x-small', margin: 0, padding: 0 }}>Original price: {getRoundedPrice(props.price)}</p>\n                                    ) : null}\n                                </span>\n                            )}\n                        </Button>\n                    </div>\n                }\n            />\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAce,SAAS,4BAA4B,KAAY;IAC5D,SAAS,gBAAgB,KAAa;QAClC,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO;IACrC;IACA,qBACI,6LAAC;QAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,aAAa;;0BAChC,6LAAC;gBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;;oBAC9B,MAAM,IAAI,KAAK,yBACZ,6LAAC,oIAAA,CAAA,UAAO;wBACJ,uBACI;;gCAAE;gCACY,MAAM,IAAI;gCAAE;8CACtB,6LAAC;oCAAK,OAAO;wCAAE,YAAY;oCAAM;8CAC7B,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;;;wBAIrB,MAAK;wBACL,8BAAgB,6LAAC;sCAAE;;;;;;;;;;+BAEvB;oBACH,MAAM,IAAI,KAAK,0BAAY,6LAAC;kCAAK;;;;;;oBACjC,MAAM,IAAI,KAAK,gCAAkB,6LAAC;kCAAK;;;;;;;;;;;;0BAE5C,6LAAC,oIAAA,CAAA,UAAO;gBACJ,MAAK;gBACL,gBAAgB,MAAM,eAAe;gBACrC,uBACI,6LAAC;oBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,oBAAoB;8BACvC,cAAA,6LAAC,2LAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,SAAS;4BACL,MAAM,KAAK;wBACf;wBACA,WAAW,2JAAA,CAAA,UAAM,CAAC,aAAa;wBAC/B,UAAU,MAAM,UAAU;kCAEzB,MAAM,aAAa,iBAChB,6LAAC;4BAAE,WAAW,2JAAA,CAAA,UAAM,CAAC,kBAAkB;sCAClC,MAAM,YAAY,iBACf;;oCAAE;kDAEE,6LAAC;;;;;oCAAK;oCAAc;kDACpB,6LAAC;wCACG,MAAM,MAAM,YAAY;wCACxB,SAAS,CAAA;4CACL,EAAE,eAAe;wCACrB;wCACA,QAAO;kDACV;;;;;;;6DAKL,6LAAC;0CAAK;;;;;;;;;;mDAId,6LAAC;;8CACG,6LAAC,kIAAA,CAAA,UAAM;oCAAC,QAAQ,gBAAgB,MAAM,QAAQ,GAAG,MAAM,KAAK,GAAG,MAAM,QAAQ,GAAG,MAAM,KAAK;;;;;;gCAAK;gCAC/F,MAAM,QAAQ,iBACX,6LAAC;oCAAK,OAAO;wCAAE,OAAO;wCAAO,YAAY;wCAAQ,aAAa;oCAAO;;wCAChE,KAAK,KAAK,CAAC,CAAC,IAAI,MAAM,QAAQ,IAAI;wCAAK;;;;;;6CAE5C;gCACH,MAAM,QAAQ,iBACX,6LAAC;oCAAE,OAAO;wCAAE,UAAU;wCAAW,QAAQ;wCAAG,SAAS;oCAAE;;wCAAG;wCAAiB,gBAAgB,MAAM,KAAK;;;;;;6CACtG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;KA5EwB", "debugId": null}}, {"offset": {"line": 2488, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CoflCoins/PurchaseElement.tsx"], "sourcesContent": ["'use client';\nimport { Card } from 'react-bootstrap'\nimport styles from './CoflCoinsPurchase.module.css'\nimport Number from '../Number/Number'\nimport GenericProviderPurchaseCard from './GenericProviderPurchaseCard'\n\nimport type { JSX } from \"react\";\n\ninterface Props {\n    coflCoinsToBuy: number\n    stripeProductId: string\n    stripePrice: number\n    paypalProductId: string\n    paypalPrice: number\n    lemonsqueezyPrice: number\n    lemonsqueezyProductId: string\n    disabledTooltip: JSX.Element | undefined\n    loadingProductId: string\n    onPayPalPay(prodcutId: string, coflCoins?: number)\n    onStripePay(producctId: string, coflCoins?: number)\n    onLemonSqeezyPay(productId: string, coflCoins?: number)\n    isDisabled: boolean\n    redirectLink?: string\n    countryCode?: string\n    discount?: number\n    isSpecial1800CoinsMultiplier?: boolean\n}\n\n// prettier-ignore\nconst EU_Countries = [\"AT\",\"BE\",\"BG\",\"HR\",\"CY\",\"CZ\",\"DK\",\"EE\",\"FI\",\"FR\",\"DE\",\"GR\",\"HU\",\"IE\",\"IT\",\"LV\",\"LT\",\"LU\",\"MT\",\"NL\",\"PL\",\"PT\",\"RO\",\"SK\",\"SI\",\"ES\",\"SE\" ]\nlet PAYPAL_STRIPE_ALLOWED = [...EU_Countries, 'GB', 'US']\n\nexport default function PurchaseElement(props: Props) {\n    let isDisabled = props.isDisabled || !props.countryCode\n\n    return (\n        <Card className={styles.premiumPlanCard} style={props.isSpecial1800CoinsMultiplier ? { width: '100%' } : {}}>\n            <Card.Header>\n                <Card.Title>\n                    <Number number={props.coflCoinsToBuy} /> CoflCoins\n                </Card.Title>\n            </Card.Header>\n            <Card.Body>\n                {props.isSpecial1800CoinsMultiplier ? (\n                    <>\n                        <p>\n                            We noticed that your CoflCoins are not a multiple of <Number number={1800} /> and therefore you would not be able to use all of them\n                            to buy premium. Here you can purchase <Number number={props.coflCoinsToBuy} /> CoflCoins to again be able to do that.\n                        </p>\n                        <p>\n                            Due to the fees we have to pay to our payment providers we sadly can't provide purchases of less than <Number number={1800} />{' '}\n                            CoflCoins at once.\n                        </p>\n                        <hr />\n                    </>\n                ) : null}\n                {props.countryCode && PAYPAL_STRIPE_ALLOWED.includes(props.countryCode) ? (\n                    <>\n                        <GenericProviderPurchaseCard\n                            type=\"PayPal\"\n                            isDisabled={isDisabled}\n                            onPay={() => {\n                                props.onPayPalPay(props.paypalProductId, props.isSpecial1800CoinsMultiplier ? props.coflCoinsToBuy : undefined)\n                            }}\n                            price={props.paypalPrice}\n                            redirectLink={props.redirectLink}\n                            discount={props.discount}\n                            isRedirecting={\n                                !props.isSpecial1800CoinsMultiplier\n                                    ? props.paypalProductId === props.loadingProductId\n                                    : `${props.paypalProductId}_${props.coflCoinsToBuy}` === props.loadingProductId\n                            }\n                            disabledTooltip={props.disabledTooltip}\n                        />\n                        <GenericProviderPurchaseCard\n                            type=\"Stripe\"\n                            isDisabled={isDisabled}\n                            onPay={() => {\n                                props.onStripePay(props.stripeProductId, props.isSpecial1800CoinsMultiplier ? props.coflCoinsToBuy : undefined)\n                            }}\n                            price={props.stripePrice}\n                            redirectLink={props.redirectLink}\n                            discount={props.discount}\n                            isRedirecting={\n                                !props.isSpecial1800CoinsMultiplier\n                                    ? props.stripeProductId === props.loadingProductId\n                                    : `${props.stripeProductId}_${props.coflCoinsToBuy}` === props.loadingProductId\n                            }\n                            disabledTooltip={props.disabledTooltip}\n                        />\n                    </>\n                ) : (\n                    <GenericProviderPurchaseCard\n                        type=\"LemonSqueezy\"\n                        isDisabled={isDisabled}\n                        onPay={() => {\n                            props.onLemonSqeezyPay(props.lemonsqueezyProductId, props.isSpecial1800CoinsMultiplier ? props.coflCoinsToBuy : undefined)\n                        }}\n                        price={props.lemonsqueezyPrice}\n                        redirectLink={props.redirectLink}\n                        discount={props.discount}\n                        isRedirecting={\n                            !props.isSpecial1800CoinsMultiplier\n                                ? props.lemonsqueezyProductId === props.loadingProductId\n                                : `${props.lemonsqueezyProductId}_${props.coflCoinsToBuy}` === props.loadingProductId\n                        }\n                        disabledTooltip={props.disabledTooltip}\n                    />\n                )}\n            </Card.Body>\n        </Card>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AA4BA,kBAAkB;AAClB,MAAM,eAAe;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAM;AAC9J,IAAI,wBAAwB;OAAI;IAAc;IAAM;CAAK;AAE1C,SAAS,gBAAgB,KAAY;IAChD,IAAI,aAAa,MAAM,UAAU,IAAI,CAAC,MAAM,WAAW;IAEvD,qBACI,6LAAC,uLAAA,CAAA,OAAI;QAAC,WAAW,2JAAA,CAAA,UAAM,CAAC,eAAe;QAAE,OAAO,MAAM,4BAA4B,GAAG;YAAE,OAAO;QAAO,IAAI,CAAC;;0BACtG,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;0BACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;sCACP,6LAAC,kIAAA,CAAA,UAAM;4BAAC,QAAQ,MAAM,cAAc;;;;;;wBAAI;;;;;;;;;;;;0BAGhD,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;oBACL,MAAM,4BAA4B,iBAC/B;;0CACI,6LAAC;;oCAAE;kDACsD,6LAAC,kIAAA,CAAA,UAAM;wCAAC,QAAQ;;;;;;oCAAQ;kDACvC,6LAAC,kIAAA,CAAA,UAAM;wCAAC,QAAQ,MAAM,cAAc;;;;;;oCAAI;;;;;;;0CAElF,6LAAC;;oCAAE;kDACuG,6LAAC,kIAAA,CAAA,UAAM;wCAAC,QAAQ;;;;;;oCAAS;oCAAI;;;;;;;0CAGvI,6LAAC;;;;;;uCAEL;oBACH,MAAM,WAAW,IAAI,sBAAsB,QAAQ,CAAC,MAAM,WAAW,kBAClE;;0CACI,6LAAC,0JAAA,CAAA,UAA2B;gCACxB,MAAK;gCACL,YAAY;gCACZ,OAAO;oCACH,MAAM,WAAW,CAAC,MAAM,eAAe,EAAE,MAAM,4BAA4B,GAAG,MAAM,cAAc,GAAG;gCACzG;gCACA,OAAO,MAAM,WAAW;gCACxB,cAAc,MAAM,YAAY;gCAChC,UAAU,MAAM,QAAQ;gCACxB,eACI,CAAC,MAAM,4BAA4B,GAC7B,MAAM,eAAe,KAAK,MAAM,gBAAgB,GAChD,GAAG,MAAM,eAAe,CAAC,CAAC,EAAE,MAAM,cAAc,EAAE,KAAK,MAAM,gBAAgB;gCAEvF,iBAAiB,MAAM,eAAe;;;;;;0CAE1C,6LAAC,0JAAA,CAAA,UAA2B;gCACxB,MAAK;gCACL,YAAY;gCACZ,OAAO;oCACH,MAAM,WAAW,CAAC,MAAM,eAAe,EAAE,MAAM,4BAA4B,GAAG,MAAM,cAAc,GAAG;gCACzG;gCACA,OAAO,MAAM,WAAW;gCACxB,cAAc,MAAM,YAAY;gCAChC,UAAU,MAAM,QAAQ;gCACxB,eACI,CAAC,MAAM,4BAA4B,GAC7B,MAAM,eAAe,KAAK,MAAM,gBAAgB,GAChD,GAAG,MAAM,eAAe,CAAC,CAAC,EAAE,MAAM,cAAc,EAAE,KAAK,MAAM,gBAAgB;gCAEvF,iBAAiB,MAAM,eAAe;;;;;;;qDAI9C,6LAAC,0JAAA,CAAA,UAA2B;wBACxB,MAAK;wBACL,YAAY;wBACZ,OAAO;4BACH,MAAM,gBAAgB,CAAC,MAAM,qBAAqB,EAAE,MAAM,4BAA4B,GAAG,MAAM,cAAc,GAAG;wBACpH;wBACA,OAAO,MAAM,iBAAiB;wBAC9B,cAAc,MAAM,YAAY;wBAChC,UAAU,MAAM,QAAQ;wBACxB,eACI,CAAC,MAAM,4BAA4B,GAC7B,MAAM,qBAAqB,KAAK,MAAM,gBAAgB,GACtD,GAAG,MAAM,qBAAqB,CAAC,CAAC,EAAE,MAAM,cAAc,EAAE,KAAK,MAAM,gBAAgB;wBAE7F,iBAAiB,MAAM,eAAe;;;;;;;;;;;;;;;;;;AAM9D;KAhFwB", "debugId": null}}, {"offset": {"line": 2697, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/CountryUtils.tsx"], "sourcesContent": ["import countryList from 'react-select-country-list'\nimport { isClientSideRendering } from './SSRUtils'\n\nexport interface Country {\n    label: string\n    value: string\n}\n\nlet countries\nexport function getCountries(): Country[] {\n    if (countries) {\n        return countries\n    }\n    let result = countryList().getData()\n    countries = result\n    return result\n}\n\nexport function getCountry(countryCode?: string) {\n    return getCountries().find(country => country.value === countryCode)\n}\n\nexport function getCountryFromUserLanguage(): Country | undefined {\n    if (!isClientSideRendering()) {\n        return undefined\n    }\n    let language = navigator.language\n    if (!language) {\n        language = 'en-US'\n    }\n    if (language.includes('-')) {\n        language = language.split('-')[1]\n    }\n    return getCountries().find(country => country.value.toLowerCase() === language.toLowerCase())\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAOA,IAAI;AACG,SAAS;IACZ,IAAI,WAAW;QACX,OAAO;IACX;IACA,IAAI,SAAS,CAAA,GAAA,wKAAA,CAAA,UAAW,AAAD,IAAI,OAAO;IAClC,YAAY;IACZ,OAAO;AACX;AAEO,SAAS,WAAW,WAAoB;IAC3C,OAAO,eAAe,IAAI,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK;AAC5D;AAEO,SAAS;IACZ,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B,OAAO;IACX;IACA,IAAI,WAAW,UAAU,QAAQ;IACjC,IAAI,CAAC,UAAU;QACX,WAAW;IACf;IACA,IAAI,SAAS,QAAQ,CAAC,MAAM;QACxB,WAAW,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE;IACrC;IACA,OAAO,eAAe,IAAI,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,WAAW,OAAO,SAAS,WAAW;AAC9F", "debugId": null}}, {"offset": {"line": 2740, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CountrySelect/CountrySelect.tsx"], "sourcesContent": ["'use client'\nimport { useRef, useState, type JSX } from 'react';\nimport { Menu, MenuItem, Typeahead } from 'react-bootstrap-typeahead'\nimport { Form, InputGroup } from 'react-bootstrap'\nimport { Country, getCountries } from '../../utils/CountryUtils'\nimport { default as TypeaheadType } from 'react-bootstrap-typeahead/types/core/Typeahead'\n\ninterface Props {\n    onCountryChange?(country: Country)\n    defaultCountry?: Country\n    isLoading?: boolean\n}\n\nexport default function CountrySelect(props: Props) {\n    const countryOptions = getCountries()\n    let [selectedCountry, setSelectedCountryCode] = useState<any>(props.defaultCountry)\n    let ref = useRef<TypeaheadType>(null)\n\n    function getCountryImage(countryCode: string): JSX.Element {\n        return (\n            <img\n                src={`https://flagcdn.com/16x12/${countryCode.toLowerCase()}.png`}\n                srcSet={`https://flagcdn.com/32x24/${countryCode.toLowerCase()}.png 2x, https://flagcdn.com/48x36/${countryCode.toLowerCase()}.png 3x`}\n                width=\"16\"\n                height=\"12\"\n                alt={countryCode}\n                style={{ marginRight: '5px' }}\n            />\n        )\n    }\n    return (\n        <div style={{ display: 'flex', alignItems: 'center', gap: 15, paddingBottom: 15 }}>\n            <label htmlFor=\"countryTypeahead\">Your Country: </label>\n            <Typeahead\n                id=\"countryTypeahead\"\n                style={{ width: 'auto' }}\n                disabled={props.isLoading}\n                placeholder={props.isLoading ? 'Loading...' : 'Select your country'}\n                ref={ref}\n                defaultSelected={selectedCountry ? [selectedCountry] : []}\n                isLoading={props.isLoading}\n                onChange={e => {\n                    if (e[0]) {\n                        setSelectedCountryCode(e[0])\n                        if (props.onCountryChange) {\n                            props.onCountryChange(e[0] as Country)\n                        }\n                    }\n                }}\n                labelKey={option => {\n                    return option ? (option as any).label : ''\n                }}\n                onFocus={e => {\n                    if (ref.current) {\n                        ref.current.clear()\n                    }\n                }}\n                options={countryOptions}\n                selectHint={(shouldSelect, event) => {\n                    return event.key === 'Enter' || shouldSelect\n                }}\n                renderInput={({ inputRef, referenceElementRef, ...inputProps }) => {\n                    return (\n                        <InputGroup>\n                            <InputGroup.Text>{selectedCountry ? getCountryImage(selectedCountry.value) : <div style={{ minWidth: 16 }} />}</InputGroup.Text>\n                            <Form.Control\n                                {...(inputProps as any)}\n                                ref={input => {\n                                    inputRef(input)\n                                    referenceElementRef(input)\n                                }}\n                            />\n                        </InputGroup>\n                    )\n                }}\n                renderMenu={(results, menuProps) => (\n                    <Menu {...menuProps}>\n                        {results.map((result, index) => {\n                            let element = result as { label: string; value: string; paginationOption?: boolean }\n                            if (element.paginationOption) {\n                                return (\n                                    <MenuItem option={element} position={index}>\n                                        More results...\n                                    </MenuItem>\n                                )\n                            }\n                            if (!element || !element.label || !element.value) {\n                                return <MenuItem option={element} position={index} />\n                            }\n                            return (\n                                <MenuItem key={element.value} option={element} position={index}>\n                                    {getCountryImage(element.value)}\n                                    {element.label}\n                                </MenuItem>\n                            )\n                        })}\n                    </Menu>\n                )}\n            ></Typeahead>\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAJA;;;;;AAae,SAAS,cAAc,KAAY;;IAC9C,MAAM,iBAAiB,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAClC,IAAI,CAAC,iBAAiB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,MAAM,cAAc;IAClF,IAAI,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAEhC,SAAS,gBAAgB,WAAmB;QACxC,qBACI,6LAAC;YACG,KAAK,CAAC,0BAA0B,EAAE,YAAY,WAAW,GAAG,IAAI,CAAC;YACjE,QAAQ,CAAC,0BAA0B,EAAE,YAAY,WAAW,GAAG,mCAAmC,EAAE,YAAY,WAAW,GAAG,OAAO,CAAC;YACtI,OAAM;YACN,QAAO;YACP,KAAK;YACL,OAAO;gBAAE,aAAa;YAAM;;;;;;IAGxC;IACA,qBACI,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,YAAY;YAAU,KAAK;YAAI,eAAe;QAAG;;0BAC5E,6LAAC;gBAAM,SAAQ;0BAAmB;;;;;;0BAClC,6LAAC,wOAAA,CAAA,YAAS;gBACN,IAAG;gBACH,OAAO;oBAAE,OAAO;gBAAO;gBACvB,UAAU,MAAM,SAAS;gBACzB,aAAa,MAAM,SAAS,GAAG,eAAe;gBAC9C,KAAK;gBACL,iBAAiB,kBAAkB;oBAAC;iBAAgB,GAAG,EAAE;gBACzD,WAAW,MAAM,SAAS;gBAC1B,UAAU,CAAA;oBACN,IAAI,CAAC,CAAC,EAAE,EAAE;wBACN,uBAAuB,CAAC,CAAC,EAAE;wBAC3B,IAAI,MAAM,eAAe,EAAE;4BACvB,MAAM,eAAe,CAAC,CAAC,CAAC,EAAE;wBAC9B;oBACJ;gBACJ;gBACA,UAAU,CAAA;oBACN,OAAO,SAAS,AAAC,OAAe,KAAK,GAAG;gBAC5C;gBACA,SAAS,CAAA;oBACL,IAAI,IAAI,OAAO,EAAE;wBACb,IAAI,OAAO,CAAC,KAAK;oBACrB;gBACJ;gBACA,SAAS;gBACT,YAAY,CAAC,cAAc;oBACvB,OAAO,MAAM,GAAG,KAAK,WAAW;gBACpC;gBACA,aAAa,CAAC,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAAG,YAAY;oBAC1D,qBACI,6LAAC,mMAAA,CAAA,aAAU;;0CACP,6LAAC,mMAAA,CAAA,aAAU,CAAC,IAAI;0CAAE,kBAAkB,gBAAgB,gBAAgB,KAAK,kBAAI,6LAAC;oCAAI,OAAO;wCAAE,UAAU;oCAAG;;;;;;;;;;;0CACxG,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;gCACR,GAAI,UAAU;gCACf,KAAK,CAAA;oCACD,SAAS;oCACT,oBAAoB;gCACxB;;;;;;;;;;;;gBAIhB;gBACA,YAAY,CAAC,SAAS,0BAClB,6LAAC,yNAAA,CAAA,OAAI;wBAAE,GAAG,SAAS;kCACd,QAAQ,GAAG,CAAC,CAAC,QAAQ;4BAClB,IAAI,UAAU;4BACd,IAAI,QAAQ,gBAAgB,EAAE;gCAC1B,qBACI,6LAAC,qOAAA,CAAA,WAAQ;oCAAC,QAAQ;oCAAS,UAAU;8CAAO;;;;;;4BAIpD;4BACA,IAAI,CAAC,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,KAAK,EAAE;gCAC9C,qBAAO,6LAAC,qOAAA,CAAA,WAAQ;oCAAC,QAAQ;oCAAS,UAAU;;;;;;4BAChD;4BACA,qBACI,6LAAC,qOAAA,CAAA,WAAQ;gCAAqB,QAAQ;gCAAS,UAAU;;oCACpD,gBAAgB,QAAQ,KAAK;oCAC7B,QAAQ,KAAK;;+BAFH,QAAQ,KAAK;;;;;wBAKpC;;;;;;;;;;;;;;;;;AAMxB;GAxFwB;KAAA", "debugId": null}}, {"offset": {"line": 2932, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CoflCoins/CoflCoinsPurchase.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState } from 'react'\nimport { Button } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport api from '../../api/ApiHelper'\nimport { useCoflCoins } from '../../utils/Hooks'\nimport styles from './CoflCoinsPurchase.module.css'\nimport PurchaseElement from './PurchaseElement'\nimport { Country, getCountry, getCountryFromUserLanguage } from '../../utils/CountryUtils'\nimport CountrySelect from '../CountrySelect/CountrySelect'\nimport { USER_COUNTRY_CODE } from '../../utils/SettingsUtils'\n\ninterface Props {\n    cancellationRightLossConfirmed: boolean\n    userCountry?: string\n}\n\nfunction Payment(props: Props) {\n    let [loadingId, setLoadingId] = useState('')\n    let [currentRedirectLink, setCurrentRedirectLink] = useState('')\n    let [showAll, setShowAll] = useState(false)\n    let [defaultCountry, setDefaultCountry] = useState<Country>()\n    let [selectedCountry, setSelectedCountry] = useState<Country>()\n    let coflCoins = useCoflCoins()\n    let isDisabled = !props.cancellationRightLossConfirmed || !selectedCountry\n\n    useEffect(() => {\n        loadDefaultCountry()\n    }, [])\n\n    async function loadDefaultCountry() {\n        let cachedCountryCode = localStorage.getItem(USER_COUNTRY_CODE)\n        if (cachedCountryCode) {\n            setDefaultCountry(getCountry(cachedCountryCode))\n            setSelectedCountry(getCountry(cachedCountryCode))\n            return\n        }\n\n        let response: Response | null = null\n        try {\n            response = await fetch('https://api.country.is')\n        } catch {\n            console.error('Failed to fetch country from api.country.is')\n        }\n\n        if (response && response.ok) {\n            let result = await response.json()\n            let country = getCountry(result.country) || getCountryFromUserLanguage()\n            setDefaultCountry(country)\n            setSelectedCountry(country)\n            localStorage.setItem(USER_COUNTRY_CODE, result.country)\n        } else {\n            let country = getCountryFromUserLanguage()\n            setDefaultCountry(country)\n            setSelectedCountry(country)\n        }\n    }\n\n    function onPayPaypal(productId: string, coflCoins?: number) {\n        setLoadingId(coflCoins ? `${productId}_${coflCoins}` : productId)\n        setCurrentRedirectLink('')\n        api.paypalPurchase(productId, coflCoins)\n            .then(data => {\n                setCurrentRedirectLink(data.directLink)\n                window.open(data.directLink, '_self')\n            })\n            .catch(onPaymentRedirectFail)\n    }\n\n    function onPayStripe(productId: string, coflCoins?: number) {\n        setLoadingId(coflCoins ? `${productId}_${coflCoins}` : productId)\n        setCurrentRedirectLink('')\n        api.stripePurchase(productId, coflCoins)\n            .then(data => {\n                setCurrentRedirectLink(data.directLink)\n                window.open(data.directLink, '_self')\n            })\n            .catch(onPaymentRedirectFail)\n    }\n\n    function onPayLemonSqueezy(productId: string, coflCoins?: number) {\n        setLoadingId(coflCoins ? `${productId}_${coflCoins}` : productId)\n        setCurrentRedirectLink('')\n        api.lemonsqueezyPurchase(productId, coflCoins)\n            .then(data => {\n                setCurrentRedirectLink(data.directLink)\n                window.open(data.directLink, '_self')\n            })\n            .catch(onPaymentRedirectFail)\n    }\n\n    function onPaymentRedirectFail() {\n        setCurrentRedirectLink('')\n        setLoadingId('')\n        toast.error('Something went wrong. Please try again.')\n    }\n\n    function getDisabledPaymentTooltip() {\n        if (!props.cancellationRightLossConfirmed) {\n            return <span>Please note the information regarding your cancellation right above.</span>\n        }\n        if (!selectedCountry) {\n            return <span>Please select your country. This information is necessary for tax purposes.</span>\n        }\n        return undefined\n    }\n    let disabledTooltip = getDisabledPaymentTooltip()\n\n    return (\n        <div>\n            <div>\n                {defaultCountry ? (\n                    <CountrySelect key=\"country-select\" isLoading={!defaultCountry} defaultCountry={defaultCountry} onCountryChange={setSelectedCountry} />\n                ) : (\n                    <CountrySelect key=\"loading-country-select\" isLoading />\n                )}\n\n                <div className={styles.productGrid}>\n                    <PurchaseElement\n                        coflCoinsToBuy={1800}\n                        loadingProductId={loadingId}\n                        redirectLink={currentRedirectLink}\n                        paypalPrice={8.69}\n                        stripePrice={8.42}\n                        lemonsqueezyPrice={8.69}\n                        disabledTooltip={disabledTooltip}\n                        isDisabled={isDisabled}\n                        onPayPalPay={onPayPaypal}\n                        onStripePay={onPayStripe}\n                        onLemonSqeezyPay={onPayLemonSqueezy}\n                        paypalProductId=\"p_cc_1800\"\n                        stripeProductId=\"s_cc_1800\"\n                        lemonsqueezyProductId=\"l_cc_1800\"\n                        countryCode={selectedCountry ? selectedCountry.value : undefined}\n                    />\n                    <PurchaseElement\n                        coflCoinsToBuy={5400}\n                        loadingProductId={loadingId}\n                        redirectLink={currentRedirectLink}\n                        paypalPrice={22.99}\n                        stripePrice={22.69}\n                        lemonsqueezyPrice={22.69}\n                        disabledTooltip={disabledTooltip}\n                        isDisabled={isDisabled}\n                        onPayPalPay={onPayPaypal}\n                        onStripePay={onPayStripe}\n                        onLemonSqeezyPay={onPayLemonSqueezy}\n                        paypalProductId=\"p_cc_5400\"\n                        stripeProductId=\"s_cc_5400\"\n                        lemonsqueezyProductId=\"l_cc_5400\"\n                        countryCode={selectedCountry ? selectedCountry.value : undefined}\n                    />\n                    {!showAll ? (\n                        <Button\n                            style={{ width: '100%' }}\n                            onClick={() => {\n                                setShowAll(true)\n                            }}\n                        >\n                            Show all CoflCoin Options\n                        </Button>\n                    ) : null}\n                    {showAll ? (\n                        <>\n                            <PurchaseElement\n                                coflCoinsToBuy={10800}\n                                loadingProductId={loadingId}\n                                redirectLink={currentRedirectLink}\n                                paypalPrice={39.69}\n                                stripePrice={38.99}\n                                lemonsqueezyPrice={39.69}\n                                disabledTooltip={disabledTooltip}\n                                isDisabled={isDisabled}\n                                onPayPalPay={onPayPaypal}\n                                onStripePay={onPayStripe}\n                                onLemonSqeezyPay={onPayLemonSqueezy}\n                                paypalProductId=\"p_cc_10800\"\n                                stripeProductId=\"s_cc_10800\"\n                                lemonsqueezyProductId=\"l_cc_10800\"\n                                countryCode={selectedCountry ? selectedCountry.value : undefined}\n                            />\n                            <PurchaseElement\n                                coflCoinsToBuy={21600}\n                                loadingProductId={loadingId}\n                                redirectLink={currentRedirectLink}\n                                paypalPrice={78.69}\n                                stripePrice={74.99}\n                                lemonsqueezyPrice={78.69}\n                                disabledTooltip={disabledTooltip}\n                                isDisabled={isDisabled}\n                                onPayPalPay={onPayPaypal}\n                                onStripePay={onPayStripe}\n                                onLemonSqeezyPay={onPayLemonSqueezy}\n                                paypalProductId=\"p_cc_21600\"\n                                stripeProductId=\"s_cc_21600\"\n                                lemonsqueezyProductId=\"l_cc_21600\"\n                                countryCode={selectedCountry ? selectedCountry.value : undefined}\n                            />\n                            {coflCoins % 1800 != 0 ? (\n                                <PurchaseElement\n                                    coflCoinsToBuy={1800 + (1800 - (coflCoins % 1800))}\n                                    loadingProductId={loadingId}\n                                    redirectLink={currentRedirectLink}\n                                    paypalPrice={(8.69 / 1800) * (1800 + (1800 - (coflCoins % 1800)))}\n                                    stripePrice={(8.42 / 1800) * (1800 + (1800 - (coflCoins % 1800)))}\n                                    lemonsqueezyPrice={(8.69 / 1800) * (1800 + (1800 - (coflCoins % 1800)))}\n                                    disabledTooltip={disabledTooltip}\n                                    isDisabled={isDisabled}\n                                    onPayPalPay={onPayPaypal}\n                                    onStripePay={onPayStripe}\n                                    onLemonSqeezyPay={onPayLemonSqueezy}\n                                    isSpecial1800CoinsMultiplier\n                                    paypalProductId=\"p_cc_1800\"\n                                    stripeProductId=\"s_cc_1800\"\n                                    lemonsqueezyProductId=\"l_cc_1800\"\n                                    countryCode={selectedCountry ? selectedCountry.value : undefined}\n                                />\n                            ) : null}\n                        </>\n                    ) : null}\n                </div>\n            </div>\n        </div>\n    )\n}\n\nexport default Payment\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AAiBA,SAAS,QAAQ,KAAY;;IACzB,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACjD,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACnD,IAAI,YAAY,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAC3B,IAAI,aAAa,CAAC,MAAM,8BAA8B,IAAI,CAAC;IAE3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN;QACJ;4BAAG,EAAE;IAEL,eAAe;QACX,IAAI,oBAAoB,aAAa,OAAO,CAAC,0HAAA,CAAA,oBAAiB;QAC9D,IAAI,mBAAmB;YACnB,kBAAkB,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;YAC7B,mBAAmB,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;YAC9B;QACJ;QAEA,IAAI,WAA4B;QAChC,IAAI;YACA,WAAW,MAAM,MAAM;QAC3B,EAAE,OAAM;YACJ,QAAQ,KAAK,CAAC;QAClB;QAEA,IAAI,YAAY,SAAS,EAAE,EAAE;YACzB,IAAI,SAAS,MAAM,SAAS,IAAI;YAChC,IAAI,UAAU,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,OAAO,KAAK,CAAA,GAAA,yHAAA,CAAA,6BAA0B,AAAD;YACrE,kBAAkB;YAClB,mBAAmB;YACnB,aAAa,OAAO,CAAC,0HAAA,CAAA,oBAAiB,EAAE,OAAO,OAAO;QAC1D,OAAO;YACH,IAAI,UAAU,CAAA,GAAA,yHAAA,CAAA,6BAA0B,AAAD;YACvC,kBAAkB;YAClB,mBAAmB;QACvB;IACJ;IAEA,SAAS,YAAY,SAAiB,EAAE,SAAkB;QACtD,aAAa,YAAY,GAAG,UAAU,CAAC,EAAE,WAAW,GAAG;QACvD,uBAAuB;QACvB,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,WAAW,WACzB,IAAI,CAAC,CAAA;YACF,uBAAuB,KAAK,UAAU;YACtC,OAAO,IAAI,CAAC,KAAK,UAAU,EAAE;QACjC,GACC,KAAK,CAAC;IACf;IAEA,SAAS,YAAY,SAAiB,EAAE,SAAkB;QACtD,aAAa,YAAY,GAAG,UAAU,CAAC,EAAE,WAAW,GAAG;QACvD,uBAAuB;QACvB,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,WAAW,WACzB,IAAI,CAAC,CAAA;YACF,uBAAuB,KAAK,UAAU;YACtC,OAAO,IAAI,CAAC,KAAK,UAAU,EAAE;QACjC,GACC,KAAK,CAAC;IACf;IAEA,SAAS,kBAAkB,SAAiB,EAAE,SAAkB;QAC5D,aAAa,YAAY,GAAG,UAAU,CAAC,EAAE,WAAW,GAAG;QACvD,uBAAuB;QACvB,oHAAA,CAAA,UAAG,CAAC,oBAAoB,CAAC,WAAW,WAC/B,IAAI,CAAC,CAAA;YACF,uBAAuB,KAAK,UAAU;YACtC,OAAO,IAAI,CAAC,KAAK,UAAU,EAAE;QACjC,GACC,KAAK,CAAC;IACf;IAEA,SAAS;QACL,uBAAuB;QACvB,aAAa;QACb,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IAChB;IAEA,SAAS;QACL,IAAI,CAAC,MAAM,8BAA8B,EAAE;YACvC,qBAAO,6LAAC;0BAAK;;;;;;QACjB;QACA,IAAI,CAAC,iBAAiB;YAClB,qBAAO,6LAAC;0BAAK;;;;;;QACjB;QACA,OAAO;IACX;IACA,IAAI,kBAAkB;IAEtB,qBACI,6LAAC;kBACG,cAAA,6LAAC;;gBACI,+BACG,6LAAC,gJAAA,CAAA,UAAa;oBAAsB,WAAW,CAAC;oBAAgB,gBAAgB;oBAAgB,iBAAiB;mBAA9F;;;;yCAEnB,6LAAC,gJAAA,CAAA,UAAa;oBAA8B,SAAS;mBAAlC;;;;;8BAGvB,6LAAC;oBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,WAAW;;sCAC9B,6LAAC,8IAAA,CAAA,UAAe;4BACZ,gBAAgB;4BAChB,kBAAkB;4BAClB,cAAc;4BACd,aAAa;4BACb,aAAa;4BACb,mBAAmB;4BACnB,iBAAiB;4BACjB,YAAY;4BACZ,aAAa;4BACb,aAAa;4BACb,kBAAkB;4BAClB,iBAAgB;4BAChB,iBAAgB;4BAChB,uBAAsB;4BACtB,aAAa,kBAAkB,gBAAgB,KAAK,GAAG;;;;;;sCAE3D,6LAAC,8IAAA,CAAA,UAAe;4BACZ,gBAAgB;4BAChB,kBAAkB;4BAClB,cAAc;4BACd,aAAa;4BACb,aAAa;4BACb,mBAAmB;4BACnB,iBAAiB;4BACjB,YAAY;4BACZ,aAAa;4BACb,aAAa;4BACb,kBAAkB;4BAClB,iBAAgB;4BAChB,iBAAgB;4BAChB,uBAAsB;4BACtB,aAAa,kBAAkB,gBAAgB,KAAK,GAAG;;;;;;wBAE1D,CAAC,wBACE,6LAAC,2LAAA,CAAA,SAAM;4BACH,OAAO;gCAAE,OAAO;4BAAO;4BACvB,SAAS;gCACL,WAAW;4BACf;sCACH;;;;;mCAGD;wBACH,wBACG;;8CACI,6LAAC,8IAAA,CAAA,UAAe;oCACZ,gBAAgB;oCAChB,kBAAkB;oCAClB,cAAc;oCACd,aAAa;oCACb,aAAa;oCACb,mBAAmB;oCACnB,iBAAiB;oCACjB,YAAY;oCACZ,aAAa;oCACb,aAAa;oCACb,kBAAkB;oCAClB,iBAAgB;oCAChB,iBAAgB;oCAChB,uBAAsB;oCACtB,aAAa,kBAAkB,gBAAgB,KAAK,GAAG;;;;;;8CAE3D,6LAAC,8IAAA,CAAA,UAAe;oCACZ,gBAAgB;oCAChB,kBAAkB;oCAClB,cAAc;oCACd,aAAa;oCACb,aAAa;oCACb,mBAAmB;oCACnB,iBAAiB;oCACjB,YAAY;oCACZ,aAAa;oCACb,aAAa;oCACb,kBAAkB;oCAClB,iBAAgB;oCAChB,iBAAgB;oCAChB,uBAAsB;oCACtB,aAAa,kBAAkB,gBAAgB,KAAK,GAAG;;;;;;gCAE1D,YAAY,QAAQ,kBACjB,6LAAC,8IAAA,CAAA,UAAe;oCACZ,gBAAgB,OAAO,CAAC,OAAQ,YAAY,IAAK;oCACjD,kBAAkB;oCAClB,cAAc;oCACd,aAAa,AAAC,OAAO,OAAQ,CAAC,OAAO,CAAC,OAAQ,YAAY,IAAK,CAAC;oCAChE,aAAa,AAAC,OAAO,OAAQ,CAAC,OAAO,CAAC,OAAQ,YAAY,IAAK,CAAC;oCAChE,mBAAmB,AAAC,OAAO,OAAQ,CAAC,OAAO,CAAC,OAAQ,YAAY,IAAK,CAAC;oCACtE,iBAAiB;oCACjB,YAAY;oCACZ,aAAa;oCACb,aAAa;oCACb,kBAAkB;oCAClB,4BAA4B;oCAC5B,iBAAgB;oCAChB,iBAAgB;oCAChB,uBAAsB;oCACtB,aAAa,kBAAkB,gBAAgB,KAAK,GAAG;;;;;2CAE3D;;2CAER;;;;;;;;;;;;;;;;;;AAKxB;GA/MS;;QAMW,kHAAA,CAAA,eAAY;;;KANvB;uCAiNM", "debugId": null}}, {"offset": {"line": 3228, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/CoflCoins/CoflCoinsDisplay.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"border\": \"CoflCoinsDisplay-module__3S8iUW__border\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 3237, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CoflCoins/CoflCoinsDisplay.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState } from 'react'\nimport { CUSTOM_EVENTS } from '../../api/ApiTypes.d'\nimport { useCoflCoins } from '../../utils/Hooks'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport Number from '../Number/Number'\nimport styles from './CoflCoinsDisplay.module.css'\nimport { toast } from 'react-toastify'\n\nexport function CoflCoinsDisplay() {\n    let coflCoins = useCoflCoins()\n    let [isLoading, setIsLoading] = useState(true)\n\n    useEffect(() => {\n        loadCoflCoins()\n        document.addEventListener(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE, loadCoflCoins)\n\n        return () => {\n            document.removeEventListener(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE, loadCoflCoins)\n        }\n    }, [])\n\n    useEffect(() => {\n        if (coflCoins !== -1) {\n            setIsLoading(false)\n        } else {\n            setIsLoading(true)\n        }\n    }, [coflCoins])\n\n    function loadCoflCoins() {}\n\n    if (isNaN(coflCoins) || coflCoins === undefined || coflCoins === null) {\n        console.error('coflCoins is not a number')\n        console.error(coflCoins)\n        toast.error('Something went wrong while loading your CoflCoins. Please try again.')\n    }\n\n    return (\n        <div className=\"cofl-coins-display\">\n            <fieldset className={styles.border} style={{ width: 'max-content', borderRadius: '15px', textAlign: 'center' }}>\n                {isLoading ? (\n                    getLoadingElement(<span />)\n                ) : (\n                    <b style={{ fontSize: 'x-large' }}>\n                        Balance: <Number number={coflCoins} /> CoflCoins\n                    </b>\n                )}\n            </fieldset>\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AASO,SAAS;;IACZ,IAAI,YAAY,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAC3B,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN;YACA,SAAS,gBAAgB,CAAC,wHAAA,CAAA,gBAAa,CAAC,oBAAoB,EAAE;YAE9D;8CAAO;oBACH,SAAS,mBAAmB,CAAC,wHAAA,CAAA,gBAAa,CAAC,oBAAoB,EAAE;gBACrE;;QACJ;qCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,IAAI,cAAc,CAAC,GAAG;gBAClB,aAAa;YACjB,OAAO;gBACH,aAAa;YACjB;QACJ;qCAAG;QAAC;KAAU;IAEd,SAAS,iBAAiB;IAE1B,IAAI,MAAM,cAAc,cAAc,aAAa,cAAc,MAAM;QACnE,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC;QACd,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IAChB;IAEA,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAS,WAAW,0JAAA,CAAA,UAAM,CAAC,MAAM;YAAE,OAAO;gBAAE,OAAO;gBAAe,cAAc;gBAAQ,WAAW;YAAS;sBACxG,YACG,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,gBAAE,6LAAC;;;;sCAEnB,6LAAC;gBAAE,OAAO;oBAAE,UAAU;gBAAU;;oBAAG;kCACtB,6LAAC,kIAAA,CAAA,UAAM;wBAAC,QAAQ;;;;;;oBAAa;;;;;;;;;;;;;;;;;AAM9D;GA1CgB;;QACI,kHAAA,CAAA,eAAY;;;KADhB", "debugId": null}}, {"offset": {"line": 3350, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/BuyPremium/BuyPremium.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"coinBalance\": \"BuyPremium-module__gXX4Mq__coinBalance\",\n  \"dropdown\": \"BuyPremium-module__gXX4Mq__dropdown\",\n  \"label\": \"BuyPremium-module__gXX4Mq__label\",\n  \"priceRangeButton\": \"BuyPremium-module__gXX4Mq__priceRangeButton\",\n  \"purchaseCard\": \"BuyPremium-module__gXX4Mq__purchaseCard\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 3362, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"label\": \"BuyPremiumConfirmationDialog-module__o90etG__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 3371, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx"], "sourcesContent": ["'use client'\nimport { <PERSON><PERSON>, Modal } from 'react-bootstrap'\nimport styles from './BuyPremiumConfirmationDialog.module.css'\nimport { getPremiumType } from '../../../utils/PremiumTypeUtils'\nimport { useState, type JSX } from 'react';\nimport { GoogleLogin } from '@react-oauth/google'\nimport { toast } from 'react-toastify'\nimport { duration } from 'moment'\n\ninterface Props {\n    type: 'prepaid' | 'subscription'\n    show: boolean\n    purchasePremiumType: PremiumType\n    purchasePremiumOption: PremiumTypeOption\n    durationString?: JSX.Element | string\n    purchasePrice: JSX.Element | string\n    activePremiumProduct: PremiumProduct\n    onHide()\n    onConfirm(googleToken: string)\n}\n\nexport default function BuyPremiumConfirmationDialog(props: Props) {\n    let [hasConfirmedLogin, setHasConfirmedLogin] = useState(false)\n    let [googleToken, setGoogleToken] = useState('')\n\n    return (\n        <Modal\n            show={props.show}\n            onHide={() => {\n                props.onHide()\n            }}\n        >\n            <Modal.Header closeButton>\n                <Modal.Title>Confirmation</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <ul>\n                    <li>\n                        <span className={styles.label}>Type:</span>\n                        {props.purchasePremiumType.label}\n                    </li>\n                    {props.durationString && (\n                        <li>\n                            <span className={styles.label}>Duration:</span>\n                            {props.purchasePremiumOption.label} {props.durationString}\n                        </li>\n                    )}\n                    <li>\n                        <span className={styles.label}>Price:</span>\n                        {props.purchasePrice}\n                    </li>\n                </ul>\n                {props.type === 'prepaid' && (\n                    <p>The time will be added to account. After you confirmed the purchase, it can't be canceled/moved to another account</p>\n                )}\n                {props.type === 'subscription' && (\n                    <p>This subscription will be automatically renewed every month. It can be canceled at any time and will then run out.</p>\n                )}\n                {props.activePremiumProduct && getPremiumType(props.activePremiumProduct)?.productId !== props.purchasePremiumType.productId ? (\n                    <div>\n                        <hr />\n                        <p style={{ color: 'yellow' }}>\n                            It seems you already have an active premium product. While the 'better' premium is active, the other will get paused.\n                        </p>\n                    </div>\n                ) : null}\n                <hr />\n                {!hasConfirmedLogin ? (\n                    <>\n                        <p>Please login again to confirm your Identity:</p>\n                        <div style={{ width: '250px', colorScheme: 'light', marginBottom: '15px' }}>\n                            <GoogleLogin\n                                onSuccess={response => {\n                                    setHasConfirmedLogin(true)\n                                    setGoogleToken(response.credential!)\n                                }}\n                                onError={() => {\n                                    toast.error('Login failed')\n                                }}\n                                theme={'filled_blue'}\n                                size={'large'}\n                            />\n                        </div>\n                    </>\n                ) : null}\n                <Button variant=\"danger\" onClick={props.onHide}>\n                    Cancel\n                </Button>\n                <Button variant=\"success\" style={{ float: 'right' }} disabled={!hasConfirmedLogin} onClick={() => props.onConfirm(googleToken)}>\n                    Confirm\n                </Button>\n            </Modal.Body>\n        </Modal>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAqBe,SAAS,6BAA6B,KAAY;;IAC7D,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACI,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM,MAAM,IAAI;QAChB,QAAQ;YACJ,MAAM,MAAM;QAChB;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;kCACP,6LAAC;;0CACG,6LAAC;;kDACG,6LAAC;wCAAK,WAAW,oMAAA,CAAA,UAAM,CAAC,KAAK;kDAAE;;;;;;oCAC9B,MAAM,mBAAmB,CAAC,KAAK;;;;;;;4BAEnC,MAAM,cAAc,kBACjB,6LAAC;;kDACG,6LAAC;wCAAK,WAAW,oMAAA,CAAA,UAAM,CAAC,KAAK;kDAAE;;;;;;oCAC9B,MAAM,qBAAqB,CAAC,KAAK;oCAAC;oCAAE,MAAM,cAAc;;;;;;;0CAGjE,6LAAC;;kDACG,6LAAC;wCAAK,WAAW,oMAAA,CAAA,UAAM,CAAC,KAAK;kDAAE;;;;;;oCAC9B,MAAM,aAAa;;;;;;;;;;;;;oBAG3B,MAAM,IAAI,KAAK,2BACZ,6LAAC;kCAAE;;;;;;oBAEN,MAAM,IAAI,KAAK,gCACZ,6LAAC;kCAAE;;;;;;oBAEN,MAAM,oBAAoB,IAAI,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,oBAAoB,GAAG,cAAc,MAAM,mBAAmB,CAAC,SAAS,iBACxH,6LAAC;;0CACG,6LAAC;;;;;0CACD,6LAAC;gCAAE,OAAO;oCAAE,OAAO;gCAAS;0CAAG;;;;;;;;;;;+BAInC;kCACJ,6LAAC;;;;;oBACA,CAAC,kCACE;;0CACI,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAI,OAAO;oCAAE,OAAO;oCAAS,aAAa;oCAAS,cAAc;gCAAO;0CACrE,cAAA,6LAAC,qKAAA,CAAA,cAAW;oCACR,WAAW,CAAA;wCACP,qBAAqB;wCACrB,eAAe,SAAS,UAAU;oCACtC;oCACA,SAAS;wCACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oCAChB;oCACA,OAAO;oCACP,MAAM;;;;;;;;;;;;uCAIlB;kCACJ,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAS,SAAS,MAAM,MAAM;kCAAE;;;;;;kCAGhD,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,OAAO;4BAAE,OAAO;wBAAQ;wBAAG,UAAU,CAAC;wBAAmB,SAAS,IAAM,MAAM,SAAS,CAAC;kCAAc;;;;;;;;;;;;;;;;;;AAMhJ;GAzEwB;KAAA", "debugId": null}}, {"offset": {"line": 3605, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/BuyPremium/BuyPremium.tsx"], "sourcesContent": ["'use client'\nimport { ChangeEvent, useState } from 'react'\nimport { <PERSON><PERSON>, Card, Form, ToggleButton, ToggleButtonGroup } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport api from '../../../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../../../api/ApiTypes.d'\nimport { useCoflCoins } from '../../../utils/Hooks'\nimport { PREMIUM_TYPES } from '../../../utils/PremiumTypeUtils'\nimport { CoflCoinsDisplay } from '../../CoflCoins/CoflCoinsDisplay'\nimport Number from '../../Number/Number'\nimport styles from './BuyPremium.module.css'\nimport BuyPremiumConfirmationDialog from '../BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog'\n\ninterface Props {\n    activePremiumProduct: PremiumProduct\n    premiumSubscriptions: PremiumSubscription[]\n    onNewActivePremiumProduct()\n}\n\nfunction BuyPremium(props: Props) {\n    let [purchasePremiumType, setPurchasePremiumType] = useState<PremiumType>(PREMIUM_TYPES[0])\n    let [purchaseSuccessfulOption, setPurchaseSuccessfulDuration] = useState<PremiumTypeOption>()\n    let [isPurchasing, setIsPurchasing] = useState(false)\n    let [purchasePremiumOption, setPurchasePremiumOption] = useState<PremiumTypeOption>(PREMIUM_TYPES[0].options[0])\n    let [showPrepaidConfirmationDialog, setShowPrepaidConfirmationDialog] = useState(false)\n    let coflCoins = useCoflCoins()\n\n    function onDurationChange(event: ChangeEvent<HTMLSelectElement>) {\n        let option = JSON.parse(event.target.value)\n        setPurchasePremiumOption(option)\n    }\n\n    function onPremiumTypeChange(productId) {\n        let selectedType = PREMIUM_TYPES.find(type => type.productId === productId)\n        if (selectedType) {\n            setPurchasePremiumType(selectedType)\n            setPurchasePremiumOption(selectedType.options[0])\n        }\n    }\n\n    function onPremiumBuy(googleToken: string) {\n        setShowPrepaidConfirmationDialog(false)\n        setIsPurchasing(true)\n\n        api.purchaseWithCoflcoins(purchasePremiumOption.productId, googleToken, purchasePremiumOption.value).then(() => {\n            document.dispatchEvent(\n                new CustomEvent(CUSTOM_EVENTS.COFLCOIN_UPDATE, {\n                    detail: { coflCoins: coflCoins - getPurchasePrice() }\n                })\n            )\n            setPurchaseSuccessfulDuration(purchasePremiumOption)\n            setIsPurchasing(false)\n            toast.success('Purchase successful')\n            props.onNewActivePremiumProduct()\n        })\n    }\n\n    function onPremiumBuyCancel() {\n        setShowPrepaidConfirmationDialog(false)\n    }\n\n    function getPurchasePrice() {\n        return purchasePremiumOption.value * purchasePremiumOption.price\n    }\n\n    function getDurationString(): string {\n        let durationString = purchasePremiumType.durationString\n        let duration = +purchasePremiumOption.value\n        if (durationString && duration > 1) {\n            durationString += 's'\n        }\n        return durationString\n    }\n\n    function getPremiumToggleButtonStyle(premiumType: PremiumType) {\n        switch (premiumType.productId) {\n            case 'premium':\n                return { color: '#32de84' }\n            case 'premium_plus':\n                return { color: '#ffaa00' }\n            default:\n                return {}\n        }\n    }\n\n    return (\n        <>\n            <Card className={styles.purchaseCard}>\n                <Card.Header>\n                    <Card.Title>Buy premium for a certain duration with your CoflCoins. Your premium time starts shortly after your purchase.</Card.Title>\n                </Card.Header>\n                <div style={{ padding: '15px' }}>\n                    {!purchaseSuccessfulOption ? (\n                        <div>\n                            <div style={{ marginBottom: '15px' }}>\n                                <label className={styles.label}>Premium type:</label>\n                                <ToggleButtonGroup\n                                    style={{ width: '250px', display: 'inline' }}\n                                    type=\"radio\"\n                                    name=\"options\"\n                                    value={purchasePremiumType.productId}\n                                    onChange={onPremiumTypeChange}\n                                >\n                                    {PREMIUM_TYPES.map(premiumType => (\n                                        <ToggleButton\n                                            id={premiumType.productId}\n                                            key={premiumType.productId}\n                                            value={premiumType.productId}\n                                            className={styles.priceRangeButton}\n                                            size=\"lg\"\n                                            variant=\"primary\"\n                                            style={getPremiumToggleButtonStyle(premiumType)}\n                                        >\n                                            {premiumType.label}\n                                        </ToggleButton>\n                                    ))}\n                                </ToggleButtonGroup>\n                                <div className={styles.coinBalance}>\n                                    <CoflCoinsDisplay />\n                                </div>\n                            </div>\n                            <div style={{ marginBottom: '15px' }}>\n                                <label className={styles.label}>Purchase Duration:</label>\n                                <Form.Select\n                                    onChange={onDurationChange}\n                                    className={styles.dropdown}\n                                    key={purchasePremiumType.productId}\n                                    defaultValue={purchasePremiumOption.value}\n                                >\n                                    {purchasePremiumType.options.map(option => {\n                                        return (\n                                            <option key={option.label} value={JSON.stringify(option)}>\n                                                {option.label}\n                                            </option>\n                                        )\n                                    })}\n                                </Form.Select>\n                                <span style={{ marginLeft: '20px' }}>{getDurationString()}</span>\n                            </div>\n                            <div>\n                                <label className={styles.label}>Price:</label>\n                                <span style={{ fontWeight: 'bold' }}>\n                                    <Number number={getPurchasePrice()} /> Coins\n                                </span>\n                            </div>\n                            {coflCoins >= getPurchasePrice() ? (\n                                <div>\n                                    <label className={styles.label}>Remaining after Purchase:</label>\n                                    <span>\n                                        <Number number={coflCoins - getPurchasePrice()} /> Coins\n                                    </span>\n                                </div>\n                            ) : null}\n                            <p style={{ marginTop: '20px' }}>This is a prepaid service. We won't automatically charge you after your premium time runs out!</p>\n                            <hr />\n                            <Button\n                                style={{ marginTop: '10px' }}\n                                variant=\"success\"\n                                onClick={() => {\n                                    setShowPrepaidConfirmationDialog(true)\n                                }}\n                                disabled={getPurchasePrice() > coflCoins || isPurchasing}\n                            >\n                                Purchase\n                            </Button>\n                            {getPurchasePrice() > coflCoins && !isPurchasing ? (\n                                <span>\n                                    <p>\n                                        <span style={{ color: 'red' }}>You don't have enough CoflCoins to buy this.</span>{' '}\n                                    </p>\n                                </span>\n                            ) : (\n                                ''\n                            )}\n                        </div>\n                    ) : (\n                        <p style={{ color: 'lime' }}>\n                            You successfully bought {purchaseSuccessfulOption.label} {getDurationString()} of {purchasePremiumType.label} for{' '}\n                            <Number number={getPurchasePrice()} /> CoflCoins!\n                        </p>\n                    )}\n                </div>\n            </Card>\n            <BuyPremiumConfirmationDialog\n                type=\"prepaid\"\n                show={showPrepaidConfirmationDialog}\n                durationString={getDurationString()}\n                purchasePremiumOption={purchasePremiumOption}\n                purchasePrice={\n                    <>\n                        <Number number={getPurchasePrice()} /> CoflCoins\n                    </>\n                }\n                purchasePremiumType={purchasePremiumType}\n                onHide={onPremiumBuyCancel}\n                onConfirm={onPremiumBuy}\n                activePremiumProduct={props.activePremiumProduct}\n            />\n        </>\n    )\n}\n\nexport default BuyPremium\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;;AAmBA,SAAS,WAAW,KAAY;;IAC5B,IAAI,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,6HAAA,CAAA,gBAAa,CAAC,EAAE;IAC1F,IAAI,CAAC,0BAA0B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACvE,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,IAAI,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,6HAAA,CAAA,gBAAa,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;IAC/G,IAAI,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjF,IAAI,YAAY,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAE3B,SAAS,iBAAiB,KAAqC;QAC3D,IAAI,SAAS,KAAK,KAAK,CAAC,MAAM,MAAM,CAAC,KAAK;QAC1C,yBAAyB;IAC7B;IAEA,SAAS,oBAAoB,SAAS;QAClC,IAAI,eAAe,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;QACjE,IAAI,cAAc;YACd,uBAAuB;YACvB,yBAAyB,aAAa,OAAO,CAAC,EAAE;QACpD;IACJ;IAEA,SAAS,aAAa,WAAmB;QACrC,iCAAiC;QACjC,gBAAgB;QAEhB,oHAAA,CAAA,UAAG,CAAC,qBAAqB,CAAC,sBAAsB,SAAS,EAAE,aAAa,sBAAsB,KAAK,EAAE,IAAI,CAAC;YACtG,SAAS,aAAa,CAClB,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,eAAe,EAAE;gBAC3C,QAAQ;oBAAE,WAAW,YAAY;gBAAmB;YACxD;YAEJ,8BAA8B;YAC9B,gBAAgB;YAChB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,MAAM,yBAAyB;QACnC;IACJ;IAEA,SAAS;QACL,iCAAiC;IACrC;IAEA,SAAS;QACL,OAAO,sBAAsB,KAAK,GAAG,sBAAsB,KAAK;IACpE;IAEA,SAAS;QACL,IAAI,iBAAiB,oBAAoB,cAAc;QACvD,IAAI,WAAW,CAAC,sBAAsB,KAAK;QAC3C,IAAI,kBAAkB,WAAW,GAAG;YAChC,kBAAkB;QACtB;QACA,OAAO;IACX;IAEA,SAAS,4BAA4B,WAAwB;QACzD,OAAQ,YAAY,SAAS;YACzB,KAAK;gBACD,OAAO;oBAAE,OAAO;gBAAU;YAC9B,KAAK;gBACD,OAAO;oBAAE,OAAO;gBAAU;YAC9B;gBACI,OAAO,CAAC;QAChB;IACJ;IAEA,qBACI;;0BACI,6LAAC,uLAAA,CAAA,OAAI;gBAAC,WAAW,gKAAA,CAAA,UAAM,CAAC,YAAY;;kCAChC,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;kCACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;sCAAC;;;;;;;;;;;kCAEhB,6LAAC;wBAAI,OAAO;4BAAE,SAAS;wBAAO;kCACzB,CAAC,yCACE,6LAAC;;8CACG,6LAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDAC/B,6LAAC;4CAAM,WAAW,gKAAA,CAAA,UAAM,CAAC,KAAK;sDAAE;;;;;;sDAChC,6LAAC,iNAAA,CAAA,oBAAiB;4CACd,OAAO;gDAAE,OAAO;gDAAS,SAAS;4CAAS;4CAC3C,MAAK;4CACL,MAAK;4CACL,OAAO,oBAAoB,SAAS;4CACpC,UAAU;sDAET,6HAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAA,4BACf,6LAAC,uMAAA,CAAA,eAAY;oDACT,IAAI,YAAY,SAAS;oDAEzB,OAAO,YAAY,SAAS;oDAC5B,WAAW,gKAAA,CAAA,UAAM,CAAC,gBAAgB;oDAClC,MAAK;oDACL,SAAQ;oDACR,OAAO,4BAA4B;8DAElC,YAAY,KAAK;mDAPb,YAAY,SAAS;;;;;;;;;;sDAWtC,6LAAC;4CAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;sDAC9B,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDAC/B,6LAAC;4CAAM,WAAW,gKAAA,CAAA,UAAM,CAAC,KAAK;sDAAE;;;;;;sDAChC,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;4CACR,UAAU;4CACV,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;4CAE1B,cAAc,sBAAsB,KAAK;sDAExC,oBAAoB,OAAO,CAAC,GAAG,CAAC,CAAA;gDAC7B,qBACI,6LAAC;oDAA0B,OAAO,KAAK,SAAS,CAAC;8DAC5C,OAAO,KAAK;mDADJ,OAAO,KAAK;;;;;4CAIjC;2CATK,oBAAoB,SAAS;;;;;sDAWtC,6LAAC;4CAAK,OAAO;gDAAE,YAAY;4CAAO;sDAAI;;;;;;;;;;;;8CAE1C,6LAAC;;sDACG,6LAAC;4CAAM,WAAW,gKAAA,CAAA,UAAM,CAAC,KAAK;sDAAE;;;;;;sDAChC,6LAAC;4CAAK,OAAO;gDAAE,YAAY;4CAAO;;8DAC9B,6LAAC,kIAAA,CAAA,UAAM;oDAAC,QAAQ;;;;;;gDAAsB;;;;;;;;;;;;;gCAG7C,aAAa,mCACV,6LAAC;;sDACG,6LAAC;4CAAM,WAAW,gKAAA,CAAA,UAAM,CAAC,KAAK;sDAAE;;;;;;sDAChC,6LAAC;;8DACG,6LAAC,kIAAA,CAAA,UAAM;oDAAC,QAAQ,YAAY;;;;;;gDAAsB;;;;;;;;;;;;2CAG1D;8CACJ,6LAAC;oCAAE,OAAO;wCAAE,WAAW;oCAAO;8CAAG;;;;;;8CACjC,6LAAC;;;;;8CACD,6LAAC,2LAAA,CAAA,SAAM;oCACH,OAAO;wCAAE,WAAW;oCAAO;oCAC3B,SAAQ;oCACR,SAAS;wCACL,iCAAiC;oCACrC;oCACA,UAAU,qBAAqB,aAAa;8CAC/C;;;;;;gCAGA,qBAAqB,aAAa,CAAC,6BAChC,6LAAC;8CACG,cAAA,6LAAC;;0DACG,6LAAC;gDAAK,OAAO;oDAAE,OAAO;gDAAM;0DAAG;;;;;;4CAAoD;;;;;;;;;;;2CAI3F;;;;;;iDAIR,6LAAC;4BAAE,OAAO;gCAAE,OAAO;4BAAO;;gCAAG;gCACA,yBAAyB,KAAK;gCAAC;gCAAE;gCAAoB;gCAAK,oBAAoB,KAAK;gCAAC;gCAAK;8CAClH,6LAAC,kIAAA,CAAA,UAAM;oCAAC,QAAQ;;;;;;gCAAsB;;;;;;;;;;;;;;;;;;0BAKtD,6LAAC,yLAAA,CAAA,UAA4B;gBACzB,MAAK;gBACL,MAAM;gBACN,gBAAgB;gBAChB,uBAAuB;gBACvB,6BACI;;sCACI,6LAAC,kIAAA,CAAA,UAAM;4BAAC,QAAQ;;;;;;wBAAsB;;;gBAG9C,qBAAqB;gBACrB,QAAQ;gBACR,WAAW;gBACX,sBAAsB,MAAM,oBAAoB;;;;;;;;AAIhE;GArLS;;QAMW,kHAAA,CAAA,eAAY;;;KANvB;uCAuLM", "debugId": null}}, {"offset": {"line": 4042, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/PlayerFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { forwardRef, Ref, useState } from 'react'\nimport { AsyncTypeahead } from 'react-bootstrap-typeahead'\nimport api from '../../../api/ApiHelper'\nimport { v4 as generateUUID } from 'uuid'\nimport Typeahead from 'react-bootstrap-typeahead/types/core/Typeahead'\ninterface Props {\n    onChange(n: string | Player)\n    disabled?: boolean\n    returnType: 'name' | 'uuid' | 'player'\n    defaultValue: string\n    ref?(ref)\n    placeholder?: string\n    isValid?: boolean\n}\n\nexport let PlayerFilterElement = forwardRef((props: Props, ref: Ref<Typeahead>) => {\n    // for player search\n    let [players, setPlayers] = useState<Player[]>([])\n    let [isLoading, setIsLoading] = useState(false)\n\n    function _onChange(selected) {\n        props.onChange(selected[0] || '')\n    }\n\n    function handlePlayerSearch(query) {\n        setIsLoading(true)\n\n        api.playerSearch(query).then(players => {\n            setPlayers(players)\n            setIsLoading(false)\n        })\n    }\n\n    return (\n        <AsyncTypeahead\n            id={generateUUID()}\n            disabled={props.disabled}\n            filterBy={() => true}\n            isLoading={isLoading}\n            labelKey=\"name\"\n            minLength={1}\n            isInvalid={!props.isValid}\n            onSearch={handlePlayerSearch}\n            defaultInputValue={props.defaultValue}\n            options={players}\n            placeholder={props.placeholder || 'Search users...'}\n            onChange={selected =>\n                _onChange(\n                    selected.map(s => {\n                        if (props.returnType === 'player') {\n                            return s\n                        }\n                        return s[props.returnType]\n                    })\n                )\n            }\n            ref={ref}\n        />\n    )\n})\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;;;AAJA;;;;;AAgBO,IAAI,oCAAsB,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UAAE,CAAC,OAAc;;IACvD,oBAAoB;IACpB,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,SAAS,UAAU,QAAQ;QACvB,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI;IAClC;IAEA,SAAS,mBAAmB,KAAK;QAC7B,aAAa;QAEb,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,CAAA;YACzB,WAAW;YACX,aAAa;QACjB;IACJ;IAEA,qBACI,6LAAC,uPAAA,CAAA,iBAAc;QACX,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;QACf,UAAU,MAAM,QAAQ;QACxB,UAAU,IAAM;QAChB,WAAW;QACX,UAAS;QACT,WAAW;QACX,WAAW,CAAC,MAAM,OAAO;QACzB,UAAU;QACV,mBAAmB,MAAM,YAAY;QACrC,SAAS;QACT,aAAa,MAAM,WAAW,IAAI;QAClC,UAAU,CAAA,WACN,UACI,SAAS,GAAG,CAAC,CAAA;gBACT,IAAI,MAAM,UAAU,KAAK,UAAU;oBAC/B,OAAO;gBACX;gBACA,OAAO,CAAC,CAAC,MAAM,UAAU,CAAC;YAC9B;QAGR,KAAK;;;;;;AAGjB", "debugId": null}}, {"offset": {"line": 4110, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"label\": \"TransferCoflCoinsSummary-module__ZfhhcG__label\",\n  \"returnButton\": \"TransferCoflCoinsSummary-module__ZfhhcG__returnButton\",\n  \"sendButton\": \"TransferCoflCoinsSummary-module__ZfhhcG__sendButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 4121, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx"], "sourcesContent": ["'use client'\nimport Image from 'next/image'\nimport { useState } from 'react'\nimport { Button } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport { v4 as generateUUID } from 'uuid'\nimport api from '../../api/ApiHelper'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport Number from '../Number/Number'\nimport styles from './TransferCoflCoinsSummary.module.css'\n\ninterface Props {\n    receiverType: 'email' | 'mcId'\n    email: string | undefined\n    player: Player | undefined\n    coflCoins: number\n    onBack()\n    onFinish()\n}\n\nfunction TransferCoflCoinsSummary(props: Props) {\n    let [reference] = useState(generateUUID())\n    let [isSending, setIsSending] = useState(false)\n\n    function onSend() {\n        setIsSending(true)\n        api.transferCoflCoins(props.email, props.player?.uuid, props.coflCoins, reference)\n            .then(() => {\n                toast.success(\n                    <span>\n                        Successfuly sent <Number number={props.coflCoins} /> CoflCoins to {props.email === '' ? props.player?.name : props.email}\n                    </span>\n                )\n                setIsSending(false)\n                props.onFinish()\n            })\n            .catch(() => {\n                setIsSending(false)\n                props.onFinish()\n            })\n    }\n\n    return (\n        <>\n            {!isSending ? (\n                <div>\n                    <p>\n                        <span className={styles.label}>Receiver:</span>\n                        {props.receiverType === 'email' ? (\n                            <span>{props.email}</span>\n                        ) : (\n                            <span>\n                                <Image\n                                    crossOrigin=\"anonymous\"\n                                    className=\"playerHeadIcon\"\n                                    src={props.player?.iconUrl || ''}\n                                    height=\"32\"\n                                    width=\"32\"\n                                    alt=\"\"\n                                    style={{ marginRight: '10px' }}\n                                    loading=\"lazy\"\n                                />\n                                {props.player!.name}\n                            </span>\n                        )}\n                    </p>\n                    <p>\n                        <span className={styles.label}>Amount: </span>\n                        {props.coflCoins} CoflCoins\n                    </p>\n\n                    <hr />\n                    <p>\n                        <span style={{ color: 'red' }}>Warning: </span>\n                        <br />\n                        Please make sure this is really the person you want to send CoflCoins to. You may not be able to get them back!\n                    </p>\n\n                    <Button className={styles.returnButton} onClick={props.onBack}>\n                        Back\n                    </Button>\n                    <Button variant=\"success\" className={styles.sendButton} onClick={onSend}>\n                        Send\n                    </Button>\n                </div>\n            ) : (\n                getLoadingElement(<p>Sending CoflCoins</p>)\n            )}\n        </>\n    )\n}\n\nexport default TransferCoflCoinsSummary\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;AAoBA,SAAS,yBAAyB,KAAY;;IAC1C,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;IACtC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,SAAS;QACL,aAAa;QACb,oHAAA,CAAA,UAAG,CAAC,iBAAiB,CAAC,MAAM,KAAK,EAAE,MAAM,MAAM,EAAE,MAAM,MAAM,SAAS,EAAE,WACnE,IAAI,CAAC;YACF,sJAAA,CAAA,QAAK,CAAC,OAAO,eACT,6LAAC;;oBAAK;kCACe,6LAAC,kIAAA,CAAA,UAAM;wBAAC,QAAQ,MAAM,SAAS;;;;;;oBAAI;oBAAe,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,EAAE,OAAO,MAAM,KAAK;;;;;;;YAGhI,aAAa;YACb,MAAM,QAAQ;QAClB,GACC,KAAK,CAAC;YACH,aAAa;YACb,MAAM,QAAQ;QAClB;IACR;IAEA,qBACI;kBACK,CAAC,0BACE,6LAAC;;8BACG,6LAAC;;sCACG,6LAAC;4BAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;wBAC9B,MAAM,YAAY,KAAK,wBACpB,6LAAC;sCAAM,MAAM,KAAK;;;;;iDAElB,6LAAC;;8CACG,6LAAC,gIAAA,CAAA,UAAK;oCACF,aAAY;oCACZ,WAAU;oCACV,KAAK,MAAM,MAAM,EAAE,WAAW;oCAC9B,QAAO;oCACP,OAAM;oCACN,KAAI;oCACJ,OAAO;wCAAE,aAAa;oCAAO;oCAC7B,SAAQ;;;;;;gCAEX,MAAM,MAAM,CAAE,IAAI;;;;;;;;;;;;;8BAI/B,6LAAC;;sCACG,6LAAC;4BAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;wBAC9B,MAAM,SAAS;wBAAC;;;;;;;8BAGrB,6LAAC;;;;;8BACD,6LAAC;;sCACG,6LAAC;4BAAK,OAAO;gCAAE,OAAO;4BAAM;sCAAG;;;;;;sCAC/B,6LAAC;;;;;wBAAK;;;;;;;8BAIV,6LAAC,2LAAA,CAAA,SAAM;oBAAC,WAAW,0KAAA,CAAA,UAAM,CAAC,YAAY;oBAAE,SAAS,MAAM,MAAM;8BAAE;;;;;;8BAG/D,6LAAC,2LAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,WAAW,0KAAA,CAAA,UAAM,CAAC,UAAU;oBAAE,SAAS;8BAAQ;;;;;;;;;;;mBAK7E,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,gBAAE,6LAAC;sBAAE;;;;;;;AAIrC;GAtES;KAAA;uCAwEM", "debugId": null}}, {"offset": {"line": 4322, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/TransferCoflCoins/TransferCoflCoins.tsx"], "sourcesContent": ["'use client'\nimport { useState } from 'react'\nimport { Button, Form } from 'react-bootstrap'\nimport { NumericFormat } from 'react-number-format'\nimport { useCoflCoins } from '../../utils/Hooks'\nimport { PlayerFilterElement } from '../FilterElement/FilterElements/PlayerFilterElement'\nimport Number from '../Number/Number'\nimport TransferCoflCoinsSummary from './TransferCoflCoinsSummary'\n\ninterface Props {\n    onFinish()\n}\n\nfunction TransferCoflCoins(props: Props) {\n    let [minecraftPlayer, setMinecraftPlayer] = useState<Player>()\n    let [email, setEmail] = useState('')\n    let [coflCoinsToSend, setCoflCoinsToSend] = useState(0)\n    let coflCoinsBalance = useCoflCoins()\n    let [showSummary, setShowSummary] = useState(false)\n\n    function onContinue() {\n        setShowSummary(true)\n    }\n\n    return (\n        <>\n            <div style={{ display: showSummary ? 'none' : 'initial' }}>\n                <p>There are 2 ways to send Cofl<PERSON>oi<PERSON> to another person:</p>\n                <ul>\n                    <li>\n                        <b>By Email:</b> Enter the email of the Google account of the receiver\n                    </li>\n                    <li>\n                        <b>By Minecraft name:</b> Search the players Minecraft name (only works if they linked their Minecraft account on the website)\n                    </li>\n                </ul>\n                <hr />\n                <div style={{ padding: '0 50px 0 50px' }}>\n                    <div>\n                        {minecraftPlayer === undefined ? (\n                            <div style={{ marginBottom: '20px' }}>\n                                By Email\n                                <Form.Control\n                                    placeholder=\"Enter Email...\"\n                                    onChange={e => {\n                                        setEmail(e.target.value)\n                                    }}\n                                />\n                            </div>\n                        ) : null}\n                        {email === '' ? (\n                            <div style={{ marginBottom: '20px' }}>\n                                By Minecraft name\n                                <PlayerFilterElement\n                                    defaultValue=\"\"\n                                    onChange={p => {\n                                        setMinecraftPlayer(p as Player)\n                                    }}\n                                    returnType={'player'}\n                                    placeholder=\"Enter Minecraft name...\"\n                                />\n                            </div>\n                        ) : null}\n                    </div>\n                    <div style={{ marginBottom: '20px' }}>\n                        Amount of CoflCoins{' '}\n                        <NumericFormat\n                            id=\"coflcoins-to-send\"\n                            onValueChange={n => {\n                                if (n.floatValue) {\n                                    setCoflCoinsToSend(n.floatValue)\n                                }\n                            }}\n                            isAllowed={value => {\n                                return value.floatValue ? value.floatValue <= coflCoinsBalance : false\n                            }}\n                            customInput={Form.Control}\n                            defaultValue={0}\n                            thousandSeparator=\".\"\n                            decimalSeparator=\",\"\n                            allowNegative={false}\n                            decimalScale={1}\n                        />\n                    </div>\n                    <span>\n                        Your current Balance: <Number number={coflCoinsBalance} />\n                    </span>\n                    <Button\n                        variant=\"success\"\n                        style={{ float: 'right' }}\n                        onClick={onContinue}\n                        disabled={coflCoinsToSend <= 0 || (email === '' && minecraftPlayer === undefined)}\n                    >\n                        Continue\n                    </Button>\n                </div>\n            </div>\n            {showSummary ? (\n                <TransferCoflCoinsSummary\n                    receiverType={minecraftPlayer !== undefined ? 'mcId' : 'email'}\n                    coflCoins={coflCoinsToSend}\n                    email={email}\n                    player={minecraftPlayer}\n                    onBack={() => {\n                        setShowSummary(false)\n                    }}\n                    onFinish={props.onFinish}\n                />\n            ) : null}\n        </>\n    )\n}\n\nexport default TransferCoflCoins\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAaA,SAAS,kBAAkB,KAAY;;IACnC,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACnD,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,IAAI,mBAAmB,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAClC,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;QACL,eAAe;IACnB;IAEA,qBACI;;0BACI,6LAAC;gBAAI,OAAO;oBAAE,SAAS,cAAc,SAAS;gBAAU;;kCACpD,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;0CACG,6LAAC;;kDACG,6LAAC;kDAAE;;;;;;oCAAa;;;;;;;0CAEpB,6LAAC;;kDACG,6LAAC;kDAAE;;;;;;oCAAsB;;;;;;;;;;;;;kCAGjC,6LAAC;;;;;kCACD,6LAAC;wBAAI,OAAO;4BAAE,SAAS;wBAAgB;;0CACnC,6LAAC;;oCACI,oBAAoB,0BACjB,6LAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAO;;4CAAG;0DAElC,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;gDACT,aAAY;gDACZ,UAAU,CAAA;oDACN,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC3B;;;;;;;;;;;+CAGR;oCACH,UAAU,mBACP,6LAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAO;;4CAAG;0DAElC,6LAAC,wKAAA,CAAA,sBAAmB;gDAChB,cAAa;gDACb,UAAU,CAAA;oDACN,mBAAmB;gDACvB;gDACA,YAAY;gDACZ,aAAY;;;;;;;;;;;+CAGpB;;;;;;;0CAER,6LAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAO;;oCAAG;oCACd;kDACpB,6LAAC,uLAAA,CAAA,gBAAa;wCACV,IAAG;wCACH,eAAe,CAAA;4CACX,IAAI,EAAE,UAAU,EAAE;gDACd,mBAAmB,EAAE,UAAU;4CACnC;wCACJ;wCACA,WAAW,CAAA;4CACP,OAAO,MAAM,UAAU,GAAG,MAAM,UAAU,IAAI,mBAAmB;wCACrE;wCACA,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;wCACzB,cAAc;wCACd,mBAAkB;wCAClB,kBAAiB;wCACjB,eAAe;wCACf,cAAc;;;;;;;;;;;;0CAGtB,6LAAC;;oCAAK;kDACoB,6LAAC,kIAAA,CAAA,UAAM;wCAAC,QAAQ;;;;;;;;;;;;0CAE1C,6LAAC,2LAAA,CAAA,SAAM;gCACH,SAAQ;gCACR,OAAO;oCAAE,OAAO;gCAAQ;gCACxB,SAAS;gCACT,UAAU,mBAAmB,KAAM,UAAU,MAAM,oBAAoB;0CAC1E;;;;;;;;;;;;;;;;;;YAKR,4BACG,6LAAC,+JAAA,CAAA,UAAwB;gBACrB,cAAc,oBAAoB,YAAY,SAAS;gBACvD,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,QAAQ;oBACJ,eAAe;gBACnB;gBACA,UAAU,MAAM,QAAQ;;;;;uBAE5B;;;AAGhB;GAlGS;;QAIkB,kHAAA,CAAA,eAAY;;;KAJ9B;uCAoGM", "debugId": null}}, {"offset": {"line": 4581, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/PremiumStatus/PremiumStatus.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"premiumStatusLabel\": \"PremiumStatus-module__89CruW__premiumStatusLabel\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 4590, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx"], "sourcesContent": ["import React from 'react'\nimport { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap'\n\ninterface CancelSubscriptionConfirmDialogProps {\n    show: boolean\n    onHide: () => void\n    onConfirm: () => void\n}\n\nconst CancelSubscriptionConfirmDialog = ({ show, onHide, onConfirm }: CancelSubscriptionConfirmDialogProps) => {\n    return (\n        <Modal show={show} onHide={onHide}>\n            <Modal.Header>\n                <Modal.Title>Confirmation</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <div>\n                    <p>Are you sure you want to cancel your subscription?</p>\n                    <div style={{ display: 'flex', gap: 5, justifyContent: 'space-between' }}>\n                        <Button variant=\"danger\" onClick={onHide}>\n                            Cancel\n                        </Button>\n                        <Button variant=\"success\" onClick={onConfirm}>\n                            Confirm\n                        </Button>\n                    </div>\n                </div>\n            </Modal.Body>\n        </Modal>\n    )\n}\n\nexport default CancelSubscriptionConfirmDialog\n"], "names": [], "mappings": ";;;;AACA;AAAA;;;AAQA,MAAM,kCAAkC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAwC;IACtG,qBACI,6LAAC,yLAAA,CAAA,QAAK;QAAC,MAAM;QAAM,QAAQ;;0BACvB,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;0BACT,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;0BACP,cAAA,6LAAC;;sCACG,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;gCAAG,gBAAgB;4BAAgB;;8CACnE,6LAAC,2LAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAS,SAAS;8CAAQ;;;;;;8CAG1C,6LAAC,2LAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE;KArBM;uCAuBS", "debugId": null}}, {"offset": {"line": 4688, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/PremiumStatus/PremiumStatus.tsx"], "sourcesContent": ["'use client'\nimport moment from 'moment'\nimport React, { useEffect, useState } from 'react'\nimport { getLocalDateAndTime } from '../../../utils/Formatter'\nimport { getHighestPriorityPremiumProduct, getPremiumLabelForSubscription, getPremiumType } from '../../../utils/PremiumTypeUtils'\nimport Tooltip from '../../Tooltip/Tooltip'\nimport styles from './PremiumStatus.module.css'\nimport { CancelOutlined } from '@mui/icons-material'\nimport CancelSubscriptionConfirmDialog from '../CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog'\n\ninterface Props {\n    products: PremiumProduct[]\n    subscriptions: PremiumSubscription[]\n    labelStyle?: React.CSSProperties\n    onSubscriptionCancel(subscription: PremiumSubscription): void\n    hasLoadingError?: boolean\n}\n\nfunction PremiumStatus(props: Props) {\n    let [highestPriorityProduct, setHighestPriorityProduct] = useState<PremiumProduct>()\n    let [productsToShow, setProductsToShow] = useState<PremiumProductWithtimeDifference[]>()\n    let [showCancelSubscriptionDialogSubscription, setShowCancelSubscriptionDialogSubscription] = useState<PremiumSubscription>()\n\n    useEffect(() => {\n        let products = props.products.map(product => {\n            return {\n                ...product,\n                timeDifference: 0\n            };\n        }).sort((a, b) => getPremiumType(b)?.priority - getPremiumType(a)?.priority);\n\n        // Hide lower tier products that are most likely bought automatically together (<1min time difference)\n        if (products.length > 1) {\n            for (let i = 1; i < products.length; i++) {\n                const diff = Math.abs(products[i - 1].expires.getTime() - products[i].expires.getTime());\n                if (diff < 60000) {\n                    if (getPremiumType(products[i - 1])?.priority > getPremiumType(products[i])?.priority) {\n                        products.splice(i, 1)\n                    } else {\n                        products.splice(i - 1, 1)\n                    }\n                    i = 0\n                } else\n                    products[i].timeDifference = diff\n            }\n        }\n\n        products = products.filter(product => product.expires > new Date())\n        setProductsToShow(products)\n        setHighestPriorityProduct(getHighestPriorityPremiumProduct(props.products))\n    }, [props.products])\n\n    function getProductListEntry(product: PremiumProductWithtimeDifference) {\n        return (\n            <>\n                <span>{getPremiumType(product)?.label}</span>\n                <Tooltip\n                    type=\"hover\"\n                    content={<span> (ends {moment(product.expires).fromNow()}{\n                        product.timeDifference > 0 ? (<>, <span className={styles.timeDifference}>{moment.duration(product.timeDifference).humanize()}</span> after</>) : null})</span>}\n                    tooltipContent={<span>At {getLocalDateAndTime(product.expires)}</span>}\n                />\n            </>\n        )\n    }\n\n    let numberOfEntriesToShow = (productsToShow?.length || 0) + (props.subscriptions?.length || 0)\n\n    return (\n        <>\n            <div>\n                {numberOfEntriesToShow > 1 ? (\n                    <div style={{ overflow: 'hidden' }}>\n                        <span className={styles.premiumStatusLabel} style={props.labelStyle}>\n                            Premium Status:\n                        </span>\n                        {props.hasLoadingError === true ? 'Premium Status could not be loaded' :\n                            <ul style={{ float: 'left' }}>\n                                {props.subscriptions.map(subscription => (\n                                    <li key={subscription.externalId}>\n                                        {' '}\n                                        <Tooltip\n                                            type=\"hover\"\n                                            content={\n                                                <span>\n                                                    {getPremiumLabelForSubscription(subscription)} (Subscription){' '}\n                                                    {subscription.endsAt && <span style={{ color: 'red', marginLeft: 5 }}>Canceled</span>}\n                                                </span>\n                                            }\n                                            tooltipContent={\n                                                <span>\n                                                    {subscription.endsAt ? (\n                                                        <span>Ends at {getLocalDateAndTime(subscription.endsAt)} </span>\n                                                    ) : (\n                                                        <span>Renews at {getLocalDateAndTime(subscription.renewsAt)}</span>\n                                                    )}\n                                                </span>\n                                            }\n                                        />\n                                        {!subscription.endsAt && (\n                                            <Tooltip\n                                                type=\"hover\"\n                                                content={\n                                                    <span style={{ color: 'red' }}>\n                                                        <CancelOutlined\n                                                            style={{ cursor: 'pointer', color: 'red', marginLeft: 5 }}\n                                                            onClick={() => {\n                                                                setShowCancelSubscriptionDialogSubscription(subscription)\n                                                            }}\n                                                        />\n                                                    </span>\n                                                }\n                                                tooltipContent={<span>Cancel subscription</span>}\n                                            />\n                                        )}\n                                    </li>\n                                ))}\n                                {productsToShow?.map(product => (\n                                    <li key={product.productSlug}>{getProductListEntry(product)}</li>\n                                ))}\n                            </ul>\n                        }\n                    </div>\n                ) : (\n                    <p>\n                        {' '}\n                        <span className={styles.premiumStatusLabel} style={props.labelStyle}>\n                            Premium Status:\n                        </span>\n                        {props.hasLoadingError === true ? 'Premium Status could not be loaded' :\n                            <>\n                                {highestPriorityProduct ? getProductListEntry({ ...highestPriorityProduct } as PremiumProductWithtimeDifference) : 'No Premium'}\n                            </>\n                        }\n                    </p>\n                )}\n            </div>\n            <CancelSubscriptionConfirmDialog\n                show={!!showCancelSubscriptionDialogSubscription}\n                onConfirm={() => {\n                    if (showCancelSubscriptionDialogSubscription) {\n                        props.onSubscriptionCancel(showCancelSubscriptionDialogSubscription)\n                        setShowCancelSubscriptionDialogSubscription(undefined)\n                    }\n                }}\n                onHide={() => {\n                    setShowCancelSubscriptionDialogSubscription(undefined)\n                }}\n            />\n        </>\n    )\n}\n\nexport default PremiumStatus\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAkBA,SAAS,cAAc,KAAY;;IAC/B,IAAI,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACjE,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACjD,IAAI,CAAC,0CAA0C,4CAA4C,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAErG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,WAAW,MAAM,QAAQ,CAAC,GAAG;oDAAC,CAAA;oBAC9B,OAAO;wBACH,GAAG,OAAO;wBACV,gBAAgB;oBACpB;gBACJ;mDAAG,IAAI;oDAAC,CAAC,GAAG,IAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;;YAEnE,sGAAsG;YACtG,IAAI,SAAS,MAAM,GAAG,GAAG;gBACrB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;oBACtC,MAAM,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;oBACrF,IAAI,OAAO,OAAO;wBACd,IAAI,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,WAAW,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,CAAC,EAAE,GAAG,UAAU;4BACnF,SAAS,MAAM,CAAC,GAAG;wBACvB,OAAO;4BACH,SAAS,MAAM,CAAC,IAAI,GAAG;wBAC3B;wBACA,IAAI;oBACR,OACI,QAAQ,CAAC,EAAE,CAAC,cAAc,GAAG;gBACrC;YACJ;YAEA,WAAW,SAAS,MAAM;2CAAC,CAAA,UAAW,QAAQ,OAAO,GAAG,IAAI;;YAC5D,kBAAkB;YAClB,0BAA0B,CAAA,GAAA,6HAAA,CAAA,mCAAgC,AAAD,EAAE,MAAM,QAAQ;QAC7E;kCAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,SAAS,oBAAoB,OAAyC;QAClE,qBACI;;8BACI,6LAAC;8BAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;;;;;;8BAChC,6LAAC,oIAAA,CAAA,UAAO;oBACJ,MAAK;oBACL,uBAAS,6LAAC;;4BAAK;4BAAQ,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,QAAQ,OAAO,EAAE,OAAO;4BAClD,QAAQ,cAAc,GAAG,kBAAK;;oCAAE;kDAAE,6LAAC;wCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;kDAAG,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,QAAQ,cAAc,EAAE,QAAQ;;;;;;oCAAU;;+CAAa;4BAAK;;;;;;;oBAC3J,8BAAgB,6LAAC;;4BAAK;4BAAI,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,OAAO;;;;;;;;;;;;;;IAI7E;IAEA,IAAI,wBAAwB,CAAC,gBAAgB,UAAU,CAAC,IAAI,CAAC,MAAM,aAAa,EAAE,UAAU,CAAC;IAE7F,qBACI;;0BACI,6LAAC;0BACI,wBAAwB,kBACrB,6LAAC;oBAAI,OAAO;wBAAE,UAAU;oBAAS;;sCAC7B,6LAAC;4BAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,kBAAkB;4BAAE,OAAO,MAAM,UAAU;sCAAE;;;;;;wBAGpE,MAAM,eAAe,KAAK,OAAO,qDAC9B,6LAAC;4BAAG,OAAO;gCAAE,OAAO;4BAAO;;gCACtB,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,6BACrB,6LAAC;;4CACI;0DACD,6LAAC,oIAAA,CAAA,UAAO;gDACJ,MAAK;gDACL,uBACI,6LAAC;;wDACI,CAAA,GAAA,6HAAA,CAAA,iCAA8B,AAAD,EAAE;wDAAc;wDAAgB;wDAC7D,aAAa,MAAM,kBAAI,6LAAC;4DAAK,OAAO;gEAAE,OAAO;gEAAO,YAAY;4DAAE;sEAAG;;;;;;;;;;;;gDAG9E,8BACI,6LAAC;8DACI,aAAa,MAAM,iBAChB,6LAAC;;4DAAK;4DAAS,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,MAAM;4DAAE;;;;;;+EAExD,6LAAC;;4DAAK;4DAAW,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,QAAQ;;;;;;;;;;;;;;;;;4CAKzE,CAAC,aAAa,MAAM,kBACjB,6LAAC,oIAAA,CAAA,UAAO;gDACJ,MAAK;gDACL,uBACI,6LAAC;oDAAK,OAAO;wDAAE,OAAO;oDAAM;8DACxB,cAAA,6LAAC,sKAAA,CAAA,UAAc;wDACX,OAAO;4DAAE,QAAQ;4DAAW,OAAO;4DAAO,YAAY;wDAAE;wDACxD,SAAS;4DACL,4CAA4C;wDAChD;;;;;;;;;;;gDAIZ,8BAAgB,6LAAC;8DAAK;;;;;;;;;;;;uCAjCzB,aAAa,UAAU;;;;;gCAsCnC,gBAAgB,IAAI,CAAA,wBACjB,6LAAC;kDAA8B,oBAAoB;uCAA1C,QAAQ,WAAW;;;;;;;;;;;;;;;;yCAM5C,6LAAC;;wBACI;sCACD,6LAAC;4BAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,kBAAkB;4BAAE,OAAO,MAAM,UAAU;sCAAE;;;;;;wBAGpE,MAAM,eAAe,KAAK,OAAO,qDAC9B;sCACK,yBAAyB,oBAAoB;gCAAE,GAAG,sBAAsB;4BAAC,KAAyC;;;;;;;;;;;;;0BAMvI,6LAAC,+LAAA,CAAA,UAA+B;gBAC5B,MAAM,CAAC,CAAC;gBACR,WAAW;oBACP,IAAI,0CAA0C;wBAC1C,MAAM,oBAAoB,CAAC;wBAC3B,4CAA4C;oBAChD;gBACJ;gBACA,QAAQ;oBACJ,4CAA4C;gBAChD;;;;;;;;AAIhB;GArIS;KAAA;uCAuIM", "debugId": null}}, {"offset": {"line": 5008, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/BuySubscription/BuySubscription.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"purchaseButton\": \"BuySubscription-module__Rzk2-G__purchaseButton\",\n  \"purchaseButtonContainer\": \"BuySubscription-module__Rzk2-G__purchaseButtonContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 5018, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/BuySubscription/BuySubscription.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport BuyPremiumConfirmationDialog from '../BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog'\nimport { PREMIUM_TYPES } from '../../../utils/PremiumTypeUtils'\nimport api from '../../../api/ApiHelper'\nimport { Button, Card, Col, Row } from 'react-bootstrap'\nimport styles from './BuySubscription.module.css'\nimport NumberElement from '../../Number/Number'\n\ninterface Props {\n    activePremiumProduct: PremiumProduct\n}\n\nfunction BuySubscription(props: Props) {\n    const [selectedPremiumType, setSelectedPremiumType] = useState<PremiumType>()\n    const [isYearOption, setIsYearOption] = useState<boolean>()\n\n    function getSubscriptionPrice() {\n        if (!selectedPremiumType) {\n            return -1\n        }\n        if (selectedPremiumType.productId === 'premium') {\n            return isYearOption ? 96.69 : 8.69\n        }\n        if (selectedPremiumType.productId === 'premium_plus') {\n            return isYearOption ? 354.20 : 35.69\n        }\n        return -1\n    }\n\n    function onSubscriptionBuyCancel() {\n        setSelectedPremiumType(undefined)\n    }\n\n    function onSubscriptionBuy(googleToken: string) {\n        if (!selectedPremiumType) {\n            return\n        }\n        let productId = ''\n        if (selectedPremiumType.productId === 'premium') {\n            productId = 'l_premium'\n        }\n        if (selectedPremiumType.productId === 'premium_plus') {\n            productId = 'l_prem_plus'\n        }\n        if (isYearOption) {\n            productId += '-year'\n        }\n\n        api.purchasePremiumSubscription(productId, googleToken).then(data => {\n            window.open(data.directLink, '_self')\n        })\n    }\n\n    return (\n        <>\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <Card.Title><b>Premium+</b></Card.Title>\n                        </Card.Header>\n                        <Card.Body>\n                            <ul>\n                                <li>top flip receive time</li>\n                                <li>all tools for analysis</li>\n                                <li>full auction archive</li>\n                            </ul>\n                            <div className={styles.purchaseButtonContainer}>\n                                <Button\n                                    variant=\"success\"\n                                    className={styles.purchaseButton}\n                                    onClick={() => {\n                                        setIsYearOption(false)\n                                        setSelectedPremiumType(PREMIUM_TYPES.find(type => type.productId === 'premium_plus'))\n                                    }}\n                                >\n                                    <NumberElement number={35.69} /> Euro (+VAT) / 4 weeks\n                                </Button>\n                                {(!props.activePremiumProduct || props.activePremiumProduct.expires.getTime() < new Date().getTime() + 3600 * 24 * 3) ?\n                                    (<><p>Use code <code>M2OTC1OQ</code> at checkout, to get an extra <b>20% discount</b> on the yearly options</p>\n                                        <Button\n                                            variant=\"success\"\n                                            className={styles.purchaseButton}\n                                            onClick={() => {\n                                                setIsYearOption(true)\n                                                setSelectedPremiumType(PREMIUM_TYPES.find(type => type.productId === 'premium_plus'))\n                                            }}\n                                        >\n                                            <NumberElement number={354.20} /> Euro (+VAT) / 52 weeks (23% off)\n                                        </Button>\n                                    </>) : null}\n                            </div>\n                        </Card.Body>\n                    </Card>\n                </Col>\n                <Col>\n                    <Card>\n                        <Card.Header>\n                            <Card.Title>Premium</Card.Title>\n                        </Card.Header>\n                        <Card.Body>\n                            <ul>\n                                <li>up to 1s slower than prem+</li>\n                                <li>a lot of tools</li>\n                                <li>extended history & filter access</li>\n                            </ul>\n                            <div className={styles.purchaseButtonContainer}>\n                                <Button\n                                    variant=\"success\"\n                                    className={styles.purchaseButton}\n                                    onClick={() => {\n                                        setSelectedPremiumType(PREMIUM_TYPES.find(type => type.productId === 'premium'))\n                                    }}\n                                >\n                                    <NumberElement number={8.69} /> Euro (+VAT) / 4 weeks\n                                </Button>\n                                {(!props.activePremiumProduct || props.activePremiumProduct.expires.getTime() < new Date().getTime() + 3600 * 24 * 3) ?\n                                    (<p>Use code <code>M2OTC1OQ</code> at checkout, to get an extra <b>20% discount</b> on the yearly options</p>) : null}\n                                <Button\n                                    variant=\"success\"\n                                    className={styles.purchaseButton}\n                                    onClick={() => {\n                                        setIsYearOption(true)\n                                        setSelectedPremiumType(PREMIUM_TYPES.find(type => type.productId === 'premium'))\n                                    }}\n                                >\n                                    <NumberElement number={96.69} /> Euro (+VAT) / 52 weeks (14% off)\n                                </Button>\n                            </div>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n            {selectedPremiumType && (\n                <BuyPremiumConfirmationDialog\n                    type=\"subscription\"\n                    show={selectedPremiumType !== undefined}\n                    purchasePremiumOption={PREMIUM_TYPES.find(type => type.productId === 'premium')?.options[0]!}\n                    purchasePrice={<>{getSubscriptionPrice()} {isYearOption ? 'per year' : 'per month'}</>}\n                    purchasePremiumType={selectedPremiumType!}\n                    onHide={onSubscriptionBuyCancel}\n                    onConfirm={onSubscriptionBuy}\n                    activePremiumProduct={props.activePremiumProduct}\n                />\n            )}\n        </>\n    )\n}\n\nexport default BuySubscription\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;;AAMA,SAAS,gBAAgB,KAAY;;IACjC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAE/C,SAAS;QACL,IAAI,CAAC,qBAAqB;YACtB,OAAO,CAAC;QACZ;QACA,IAAI,oBAAoB,SAAS,KAAK,WAAW;YAC7C,OAAO,eAAe,QAAQ;QAClC;QACA,IAAI,oBAAoB,SAAS,KAAK,gBAAgB;YAClD,OAAO,eAAe,SAAS;QACnC;QACA,OAAO,CAAC;IACZ;IAEA,SAAS;QACL,uBAAuB;IAC3B;IAEA,SAAS,kBAAkB,WAAmB;QAC1C,IAAI,CAAC,qBAAqB;YACtB;QACJ;QACA,IAAI,YAAY;QAChB,IAAI,oBAAoB,SAAS,KAAK,WAAW;YAC7C,YAAY;QAChB;QACA,IAAI,oBAAoB,SAAS,KAAK,gBAAgB;YAClD,YAAY;QAChB;QACA,IAAI,cAAc;YACd,aAAa;QACjB;QAEA,oHAAA,CAAA,UAAG,CAAC,2BAA2B,CAAC,WAAW,aAAa,IAAI,CAAC,CAAA;YACzD,OAAO,IAAI,CAAC,KAAK,UAAU,EAAE;QACjC;IACJ;IAEA,qBACI;;0BACI,6LAAC,qLAAA,CAAA,MAAG;;kCACA,6LAAC,qLAAA,CAAA,MAAG;kCACA,cAAA,6LAAC,uLAAA,CAAA,OAAI;;8CACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;8CACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kDAAC,cAAA,6LAAC;sDAAE;;;;;;;;;;;;;;;;8CAEnB,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;sDACN,6LAAC;;8DACG,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,uBAAuB;;8DAC1C,6LAAC,2LAAA,CAAA,SAAM;oDACH,SAAQ;oDACR,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;oDAChC,SAAS;wDACL,gBAAgB;wDAChB,uBAAuB,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;oDACzE;;sEAEA,6LAAC,kIAAA,CAAA,UAAa;4DAAC,QAAQ;;;;;;wDAAS;;;;;;;gDAElC,CAAC,MAAM,oBAAoB,IAAI,MAAM,oBAAoB,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,OAAO,KAAK,kBAC9G;;sEAAE,6LAAC;;gEAAE;8EAAS,6LAAC;8EAAK;;;;;;gEAAe;8EAA8B,6LAAC;8EAAE;;;;;;gEAAgB;;;;;;;sEACjF,6LAAC,2LAAA,CAAA,SAAM;4DACH,SAAQ;4DACR,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;4DAChC,SAAS;gEACL,gBAAgB;gEAChB,uBAAuB,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;4DACzE;;8EAEA,6LAAC,kIAAA,CAAA,UAAa;oEAAC,QAAQ;;;;;;gEAAU;;;;;;;;mEAElC;;;;;;;;;;;;;;;;;;;;;;;;kCAK3B,6LAAC,qLAAA,CAAA,MAAG;kCACA,cAAA,6LAAC,uLAAA,CAAA,OAAI;;8CACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;8CACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kDAAC;;;;;;;;;;;8CAEhB,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;sDACN,6LAAC;;8DACG,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAW,0KAAA,CAAA,UAAM,CAAC,uBAAuB;;8DAC1C,6LAAC,2LAAA,CAAA,SAAM;oDACH,SAAQ;oDACR,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;oDAChC,SAAS;wDACL,uBAAuB,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;oDACzE;;sEAEA,6LAAC,kIAAA,CAAA,UAAa;4DAAC,QAAQ;;;;;;wDAAQ;;;;;;;gDAEjC,CAAC,MAAM,oBAAoB,IAAI,MAAM,oBAAoB,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,OAAO,KAAK,kBAC9G,6LAAC;;wDAAE;sEAAS,6LAAC;sEAAK;;;;;;wDAAe;sEAA8B,6LAAC;sEAAE;;;;;;wDAAgB;;;;;;2DAA8B;8DACrH,6LAAC,2LAAA,CAAA,SAAM;oDACH,SAAQ;oDACR,WAAW,0KAAA,CAAA,UAAM,CAAC,cAAc;oDAChC,SAAS;wDACL,gBAAgB;wDAChB,uBAAuB,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK;oDACzE;;sEAEA,6LAAC,kIAAA,CAAA,UAAa;4DAAC,QAAQ;;;;;;wDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOvD,qCACG,6LAAC,yLAAA,CAAA,UAA4B;gBACzB,MAAK;gBACL,MAAM,wBAAwB;gBAC9B,uBAAuB,6HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK,YAAY,OAAO,CAAC,EAAE;gBAC3F,6BAAe;;wBAAG;wBAAuB;wBAAE,eAAe,aAAa;;;gBACvE,qBAAqB;gBACrB,QAAQ;gBACR,WAAW;gBACX,sBAAsB,MAAM,oBAAoB;;;;;;;;AAKpE;GAvIS;KAAA;uCAyIM", "debugId": null}}, {"offset": {"line": 5418, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/Premium.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState } from 'react'\nimport GoogleSignIn from '../GoogleSignIn/GoogleSignIn'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport { Button, Card, Form, Modal } from 'react-bootstrap'\nimport NavBar from '../NavBar/NavBar'\nimport PremiumFeatures from './PremiumFeatures/PremiumFeatures'\nimport api from '../../api/ApiHelper'\nimport styles from './Premium.module.css'\nimport CoflCoinsPurchase from '../CoflCoins/CoflCoinsPurchase'\nimport BuyPremium from './BuyPremium/BuyPremium'\nimport TransferCoflCoins from '../TransferCoflCoins/TransferCoflCoins'\nimport { CANCELLATION_RIGHT_CONFIRMED } from '../../utils/SettingsUtils'\nimport { getHighestPriorityPremiumProduct } from '../../utils/PremiumTypeUtils'\nimport PremiumStatus from './PremiumStatus/PremiumStatus'\nimport { toast } from 'react-toastify'\nimport BuySubscription from './BuySubscription/BuySubscription'\n\nfunction Premium() {\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [hasPremium, setHasPremium] = useState<boolean>()\n    let [activePremiumProduct, setActivePremiumProduct] = useState<PremiumProduct>()\n    let [products, setProducts] = useState<PremiumProduct[]>([])\n    let [premiumSubscriptions, setPremiumSubscriptions] = useState<PremiumSubscription[]>([])\n    let [isLoading, setIsLoading] = useState(false)\n    let [showSendCoflCoins, setShowSendCoflCoins] = useState(false)\n    let [cancellationRightLossConfirmed, setCancellationRightLossConfirmed] = useState(false)\n    let [isSSR, setIsSSR] = useState(true)\n\n    useEffect(() => {\n        setIsSSR(false)\n        setCancellationRightLossConfirmed(localStorage.getItem(CANCELLATION_RIGHT_CONFIRMED) === 'true')\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    function loadPremiumProducts(): Promise<void> {\n        return api.refreshLoadPremiumProducts(products => {\n            products = products.filter(product => product.expires.getTime() > new Date().getTime())\n            setProducts(products)\n            let activePremiumProduct = getHighestPriorityPremiumProduct(products)\n\n            if (!activePremiumProduct) {\n                setHasPremium(false)\n            } else {\n                setHasPremium(true)\n                setActivePremiumProduct(activePremiumProduct)\n            }\n        })\n    }\n\n    function loadPremiumSubscriptions(): Promise<void> {\n        return api.getPremiumSubscriptions().then(subscriptions => {\n            subscriptions = subscriptions.filter(subscription => !subscription.endsAt || subscription.endsAt.getTime() > new Date().getTime())\n            setPremiumSubscriptions(subscriptions)\n        })\n    }\n\n    function onSubscriptionCancel(subscription: PremiumSubscription) {\n        api.cancelPremiumSubscription(subscription.externalId).then(() => {\n            loadPremiumSubscriptions()\n            toast.success('Subscription cancelled')\n        })\n    }\n\n    function onLogin() {\n        let googleId = sessionStorage.getItem('googleId')\n        if (googleId) {\n            setIsLoading(true)\n            setIsLoggedIn(true)\n            Promise.all([loadPremiumProducts(), loadPremiumSubscriptions()]).then(() => {\n                setIsLoading(false)\n            })\n        }\n    }\n\n    function onLoginFail() {\n        setIsLoggedIn(false)\n        setHasPremium(false)\n    }\n\n    return (\n        <div>\n            <h2>\n                <NavBar />\n                Premium\n            </h2>\n            <hr />\n            {isLoading ? (\n                getLoadingElement()\n            ) : !isLoggedIn ? (\n                <div>\n                    <p style={{ color: 'yellow', margin: 0 }}>To use Premium please login with Google.</p>\n                </div>\n            ) : hasPremium ? (\n                <p style={{ color: '#00bc8c' }}>You have a Premium account. Thank you for your support.</p>\n            ) : (\n                <div>\n                    <p style={{ color: 'red', margin: 0 }}>You do not have a Premium account.</p>\n                </div>\n            )}\n            {isLoggedIn && !hasPremium ? (\n                <p>\n                    <a href=\"#buyPremium\">I want Premium!</a>\n                </p>\n            ) : null}\n            <hr />\n            <div style={{ marginBottom: '20px' }}>\n                {isLoggedIn ? <PremiumStatus products={products} subscriptions={premiumSubscriptions} onSubscriptionCancel={onSubscriptionCancel} /> : null}\n                <GoogleSignIn onAfterLogin={onLogin} onLoginFail={onLoginFail} />\n                <div>{isLoading ? getLoadingElement() : ''}</div>\n            </div>\n            {isLoggedIn ? (\n                <div style={{ marginBottom: '20px' }}>\n                    <hr />\n                    <h2>Subscriptions</h2>\n                    <BuySubscription activePremiumProduct={activePremiumProduct!} />\n                </div>\n            ) : null}\n            {isLoggedIn ? (\n                <div style={{ marginBottom: '20px' }}>\n                    <hr />\n                    <h2>Prepaid</h2>\n                    <BuyPremium\n                        activePremiumProduct={activePremiumProduct!}\n                        premiumSubscriptions={premiumSubscriptions}\n                        onNewActivePremiumProduct={loadPremiumProducts}\n                    />\n                </div>\n            ) : null}\n            {isLoggedIn ? (\n                <div style={{ marginBottom: '20px' }}>\n                    <hr />\n                    <h2>\n                        CoflCoins\n                        <Button\n                            className={styles.sendCoflCoinsButton}\n                            onClick={() => {\n                                setShowSendCoflCoins(true)\n                            }}\n                        >\n                            Send CoflCoins\n                        </Button>\n                        <Modal\n                            size={'lg'}\n                            show={showSendCoflCoins}\n                            onHide={() => {\n                                setShowSendCoflCoins(false)\n                            }}\n                        >\n                            <Modal.Header closeButton>\n                                <Modal.Title>Send CoflCoins</Modal.Title>\n                            </Modal.Header>\n                            <Modal.Body>\n                                <TransferCoflCoins\n                                    onFinish={() => {\n                                        setShowSendCoflCoins(false)\n                                    }}\n                                />\n                            </Modal.Body>\n                        </Modal>\n                    </h2>\n                    {!cancellationRightLossConfirmed ? (\n                        <div style={{ paddingBottom: '15px' }}>\n                            <Form.Check\n                                id={'cancellationRightCheckbox'}\n                                className={styles.cancellationRightCheckbox}\n                                defaultChecked={isSSR ? false : localStorage.getItem(CANCELLATION_RIGHT_CONFIRMED) === 'true'}\n                                onChange={e => {\n                                    localStorage.setItem(CANCELLATION_RIGHT_CONFIRMED, e.target.checked.toString())\n                                    setCancellationRightLossConfirmed(e.target.checked)\n                                }}\n                                inline\n                            />\n                            <label htmlFor={'cancellationRightCheckbox'}>\n                                By buying one of the following products, you confirm the immediate execution of the contract, hereby losing your cancellation\n                                right.\n                            </label>\n                        </div>\n                    ) : null}\n                    <CoflCoinsPurchase cancellationRightLossConfirmed={cancellationRightLossConfirmed} />\n                </div>\n            ) : null}\n            <hr />\n            <h2>Features</h2>\n            <Card className={styles.premiumCard}>\n                <Card.Header>\n                    <Card.Title>\n                        {hasPremium ? (\n                            <p>\n                                Thank you for your support. You have a Premium account. By buying another Premium plan you can extend your time. You can use the\n                                following premium features:\n                            </p>\n                        ) : (\n                            <p>Log in and buy Premium to support us and get access to these features</p>\n                        )}\n                    </Card.Title>\n                </Card.Header>\n                <div style={{ padding: '15px' }}>\n                    <PremiumFeatures />\n                </div>\n            </Card>\n        </div>\n    )\n}\n\nexport default Premium\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;;;;;;;;;AAkBA,SAAS;;IACL,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACzC,IAAI,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC7D,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,IAAI,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IACxF,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,gCAAgC,kCAAkC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnF,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,SAAS;YACT,kCAAkC,aAAa,OAAO,CAAC,0HAAA,CAAA,+BAA4B,MAAM;QACzF,uDAAuD;QAC3D;4BAAG,EAAE;IAEL,SAAS;QACL,OAAO,oHAAA,CAAA,UAAG,CAAC,0BAA0B,CAAC,CAAA;YAClC,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO;YACpF,YAAY;YACZ,IAAI,uBAAuB,CAAA,GAAA,6HAAA,CAAA,mCAAgC,AAAD,EAAE;YAE5D,IAAI,CAAC,sBAAsB;gBACvB,cAAc;YAClB,OAAO;gBACH,cAAc;gBACd,wBAAwB;YAC5B;QACJ;IACJ;IAEA,SAAS;QACL,OAAO,oHAAA,CAAA,UAAG,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAA;YACtC,gBAAgB,cAAc,MAAM,CAAC,CAAA,eAAgB,CAAC,aAAa,MAAM,IAAI,aAAa,MAAM,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO;YAC/H,wBAAwB;QAC5B;IACJ;IAEA,SAAS,qBAAqB,YAAiC;QAC3D,oHAAA,CAAA,UAAG,CAAC,yBAAyB,CAAC,aAAa,UAAU,EAAE,IAAI,CAAC;YACxD;YACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAClB;IACJ;IAEA,SAAS;QACL,IAAI,WAAW,eAAe,OAAO,CAAC;QACtC,IAAI,UAAU;YACV,aAAa;YACb,cAAc;YACd,QAAQ,GAAG,CAAC;gBAAC;gBAAuB;aAA2B,EAAE,IAAI,CAAC;gBAClE,aAAa;YACjB;QACJ;IACJ;IAEA,SAAS;QACL,cAAc;QACd,cAAc;IAClB;IAEA,qBACI,6LAAC;;0BACG,6LAAC;;kCACG,6LAAC,kIAAA,CAAA,UAAM;;;;;oBAAG;;;;;;;0BAGd,6LAAC;;;;;YACA,YACG,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,MAChB,CAAC,2BACD,6LAAC;0BACG,cAAA,6LAAC;oBAAE,OAAO;wBAAE,OAAO;wBAAU,QAAQ;oBAAE;8BAAG;;;;;;;;;;uBAE9C,2BACA,6LAAC;gBAAE,OAAO;oBAAE,OAAO;gBAAU;0BAAG;;;;;qCAEhC,6LAAC;0BACG,cAAA,6LAAC;oBAAE,OAAO;wBAAE,OAAO;wBAAO,QAAQ;oBAAE;8BAAG;;;;;;;;;;;YAG9C,cAAc,CAAC,2BACZ,6LAAC;0BACG,cAAA,6LAAC;oBAAE,MAAK;8BAAc;;;;;;;;;;uBAE1B;0BACJ,6LAAC;;;;;0BACD,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;oBAC9B,2BAAa,6LAAC,2JAAA,CAAA,UAAa;wBAAC,UAAU;wBAAU,eAAe;wBAAsB,sBAAsB;;;;;+BAA2B;kCACvI,6LAAC,8IAAA,CAAA,UAAY;wBAAC,cAAc;wBAAS,aAAa;;;;;;kCAClD,6LAAC;kCAAK,YAAY,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,MAAM;;;;;;;;;;;;YAE3C,2BACG,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCAC/B,6LAAC;;;;;kCACD,6LAAC;kCAAG;;;;;;kCACJ,6LAAC,+JAAA,CAAA,UAAe;wBAAC,sBAAsB;;;;;;;;;;;uBAE3C;YACH,2BACG,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCAC/B,6LAAC;;;;;kCACD,6LAAC;kCAAG;;;;;;kCACJ,6LAAC,qJAAA,CAAA,UAAU;wBACP,sBAAsB;wBACtB,sBAAsB;wBACtB,2BAA2B;;;;;;;;;;;uBAGnC;YACH,2BACG,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCAC/B,6LAAC;;;;;kCACD,6LAAC;;4BAAG;0CAEA,6LAAC,2LAAA,CAAA,SAAM;gCACH,WAAW,+IAAA,CAAA,UAAM,CAAC,mBAAmB;gCACrC,SAAS;oCACL,qBAAqB;gCACzB;0CACH;;;;;;0CAGD,6LAAC,yLAAA,CAAA,QAAK;gCACF,MAAM;gCACN,MAAM;gCACN,QAAQ;oCACJ,qBAAqB;gCACzB;;kDAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;wCAAC,WAAW;kDACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;sDAAC;;;;;;;;;;;kDAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;kDACP,cAAA,6LAAC,wJAAA,CAAA,UAAiB;4CACd,UAAU;gDACN,qBAAqB;4CACzB;;;;;;;;;;;;;;;;;;;;;;;oBAKf,CAAC,+CACE,6LAAC;wBAAI,OAAO;4BAAE,eAAe;wBAAO;;0CAChC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCACP,IAAI;gCACJ,WAAW,+IAAA,CAAA,UAAM,CAAC,yBAAyB;gCAC3C,gBAAgB,QAAQ,QAAQ,aAAa,OAAO,CAAC,0HAAA,CAAA,+BAA4B,MAAM;gCACvF,UAAU,CAAA;oCACN,aAAa,OAAO,CAAC,0HAAA,CAAA,+BAA4B,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;oCAC5E,kCAAkC,EAAE,MAAM,CAAC,OAAO;gCACtD;gCACA,MAAM;;;;;;0CAEV,6LAAC;gCAAM,SAAS;0CAA6B;;;;;;;;;;;+BAKjD;kCACJ,6LAAC,gJAAA,CAAA,UAAiB;wBAAC,gCAAgC;;;;;;;;;;;uBAEvD;0BACJ,6LAAC;;;;;0BACD,6LAAC;0BAAG;;;;;;0BACJ,6LAAC,uLAAA,CAAA,OAAI;gBAAC,WAAW,+IAAA,CAAA,UAAM,CAAC,WAAW;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;kCACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;sCACN,2BACG,6LAAC;0CAAE;;;;;qDAKH,6LAAC;0CAAE;;;;;;;;;;;;;;;;kCAIf,6LAAC;wBAAI,OAAO;4BAAE,SAAS;wBAAO;kCAC1B,cAAA,6LAAC,+JAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;AAKpC;GAzLS;KAAA;uCA2LM", "debugId": null}}]}