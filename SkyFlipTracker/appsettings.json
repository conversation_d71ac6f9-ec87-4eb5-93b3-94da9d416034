{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "DB_CONNECTION": "server=mariadb;user=root;password=cofl_secure_password_2024;database=tracker", "JAEGER_SAMPLER_TYPE": "ratelimiting", "KAFKA_HOST": "localhost", "API_BASE_URL": "http://localhost:5005", "CRAFTS_BASE_URL": "http://localhost:5009", "ITEMS_BASE_URL": "http://localhost:5014", "PROXY_BASE_URL": "http://localhost:5029", "BAZAAR_BASE_URL": "http://localhost:5011", "MCCONNECT_BASE_URL": "http://localhost:5021", "PLAYERSTATE_BASE_URL": "http://localhost:5025", "TOPICS": {"LOW_PRICED": "sky-lowpriced", "FLIP_EVENT": "sky-flipevent", "SOLD_AUCTION": "sky-soldauction", "FLIP_SUMMARY": "sky-flipsummary", "LOAD_FLIPS": "sky-loadflips", "NEW_AUCTION": "sky-newauction", "PLAYER_TRADE": "sky-player-trade"}, "KAFKA": {"BROKERS": "kafka:9092", "USERNAME": null, "PASSWORD": "", "TLS": {"CERTIFICATE_LOCATION": "", "CA_LOCATION": "", "KEY_LOCATION": ""}, "REPLICATION_FACTOR": "1"}, "CASSANDRA": {"HOSTS": "localhost", "USER": "cassandra", "PASSWORD": "cassandra", "REPLICATION_CLASS": "SimpleStrategy", "REPLICATION_FACTOR": 1, "KEYSPACE": "flips"}, "JAEGER_SAMPLER_PARAM": "2", "MARIADB_VERSION": "10.5.5", "OTEL_EXPORTER_OTLP_TRACES_ENDPOINT": "http://jaeger", "JAEGER_SERVICE_NAME": "sky-tracker"}